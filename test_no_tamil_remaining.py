#!/usr/bin/env python3
"""
Test that no Tamil characters remain after transliteration
"""

import re
from googletrans import Translator
from tamil_to_english_transcript import google_transliterate_tamil, simple_tamil_transliterate, has_tamil_characters

# Tamil unicode range
tamil_chars_re = re.compile(r'[\u0B80-\u0BFF]')

def test_no_tamil_remaining():
    """Test that transliteration removes ALL Tamil characters"""
    
    translator = Translator()
    
    # Test cases with the problematic characters
    test_cases = [
        "த",  # Individual characters
        "ட", 
        "ப", 
        "வ", 
        "ீ", 
        "்", 
        "ி", 
        "ா", 
        "க", 
        "ல", 
        "ன", 
        "ம",
        "தமிழ்",  # Common word
        "வணக்கம்",  # Greeting
        "நன்றி",  # Thank you
        "அப்போது எனக்கு 27 வயதுதான்",  # Full sentence
    ]
    
    print("🧪 Testing Tamil Character Removal:")
    print("=" * 60)
    
    all_passed = True
    
    for i, tamil_text in enumerate(test_cases, 1):
        print(f"\nTest {i}: '{tamil_text}'")
        
        # Test simple mapping
        simple_result = simple_tamil_transliterate(tamil_text)
        simple_has_tamil = has_tamil_characters(simple_result)
        
        # Test Google hybrid
        google_result = google_transliterate_tamil(tamil_text, translator)
        google_has_tamil = has_tamil_characters(google_result)
        
        print(f"  Simple mapping: '{simple_result}' (Tamil remaining: {simple_has_tamil})")
        print(f"  Google hybrid:  '{google_result}' (Tamil remaining: {google_has_tamil})")
        
        if simple_has_tamil:
            print(f"  ❌ FAIL: Simple mapping still has Tamil characters!")
            # Find which characters
            remaining = tamil_chars_re.findall(simple_result)
            print(f"      Remaining: {set(remaining)}")
            all_passed = False
        else:
            print(f"  ✅ PASS: Simple mapping clean")
        
        if google_has_tamil:
            print(f"  ❌ FAIL: Google hybrid still has Tamil characters!")
            remaining = tamil_chars_re.findall(google_result)
            print(f"      Remaining: {set(remaining)}")
            all_passed = False
        else:
            print(f"  ✅ PASS: Google hybrid clean")
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED! No Tamil characters should remain in output.")
    else:
        print("❌ SOME TESTS FAILED! Tamil characters are still present.")
    
    return all_passed

if __name__ == "__main__":
    test_no_tamil_remaining()
