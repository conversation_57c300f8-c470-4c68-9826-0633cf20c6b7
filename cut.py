import ffmpeg

def crop_video(input_file, output_file, crop_x, crop_y, crop_w, crop_h):
    (
        ffmpeg
        .input(input_file)
        .crop(crop_x, crop_y, crop_w, crop_h)
        .output(output_file)
        .run(overwrite_output=True)
    )

input_path = r'Z:\sun_network_footage\4th_dimension\mp4\mov&mp4\org_mp4\EPI 122 A.mp4'
output_path = r'Z:\sun_network_footage\4th_dimension\mp4\mov&mp4\out_mp4\cropped_output.mp4'

crop_video(input_path, output_path, 650, 0, 1280, 1080)
