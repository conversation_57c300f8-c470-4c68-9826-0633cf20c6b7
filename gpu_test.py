import cv2
import numpy as np

def check_opencv_build():
    print("🔍 OpenCV Build Information:")
    print(f"OpenCV Version: {cv2.__version__}")
    
    # Check build info
    build_info = cv2.getBuildInformation()
    
    # Look for CUDA in build info
    if "CUDA" in build_info:
        print("✅ OpenCV was compiled with CUDA support")
        cuda_lines = [line for line in build_info.split('\n') if 'CUDA' in line]
        for line in cuda_lines[:5]:  # Show first 5 CUDA-related lines
            print(f"   {line.strip()}")
    else:
        print("❌ OpenCV was NOT compiled with CUDA support")
    
    print("\n" + "="*50)

def check_cuda_devices():
    print("🔍 CUDA Device Check:")
    try:
        device_count = cv2.cuda.getCudaEnabledDeviceCount()
        print(f"CUDA Enabled Device Count: {device_count}")
        
        if device_count > 0:
            for i in range(device_count):
                try:
                    cv2.cuda.setDevice(i)
                    print(f"✅ Device {i}: Successfully set as active")
                    
                    # Try to create a simple GPU matrix
                    test_mat = cv2.cuda_GpuMat()
                    test_array = np.ones((100, 100, 3), dtype=np.uint8) * 255
                    test_mat.upload(test_array)
                    result = test_mat.download()
                    print(f"   Device {i}: GPU memory operations working")
                    
                except Exception as e:
                    print(f"❌ Device {i}: Error - {e}")
        else:
            print("❌ No CUDA devices detected")
            
    except Exception as e:
        print(f"❌ CUDA module error: {e}")
    
    print("\n" + "="*50)

def check_gpu_operations():
    print("🔍 GPU Operations Test:")
    try:
        # Test basic GPU operations
        test_img = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # Upload to GPU
        gpu_img = cv2.cuda_GpuMat()
        gpu_img.upload(test_img)
        print("✅ GPU upload successful")
        
        # Test color conversion
        gpu_gray = cv2.cuda.cvtColor(gpu_img, cv2.COLOR_BGR2GRAY)
        print("✅ GPU color conversion successful")
        
        # Test resize
        gpu_resized = cv2.cuda.resize(gpu_img, (320, 240))
        print("✅ GPU resize successful")
        
        # Download result
        result = gpu_resized.download()
        print("✅ GPU download successful")
        print(f"   Result shape: {result.shape}")
        
    except Exception as e:
        print(f"❌ GPU operations failed: {e}")

def main():
    print("🚀 GPU Capability Test for OpenCV\n")
    
    check_opencv_build()
    check_cuda_devices()
    check_gpu_operations()
    
    print("\n🏁 Test Complete!")
    print("\nIf CUDA is not available, you can:")
    print("1. Install opencv-contrib-python with CUDA support")
    print("2. Use CPU processing (which is still quite fast)")
    print("3. Check NVIDIA drivers and CUDA toolkit installation")

if __name__ == "__main__":
    main()
