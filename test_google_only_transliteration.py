#!/usr/bin/env python3
"""
Test Google-only transliteration methods
"""

from googletrans import Translator
import re

# Tamil unicode range
tamil_chars_re = re.compile(r'[\u0B80-\u0BFF]')

def has_tamil_characters(text):
    """Check if text contains any Tamil characters"""
    return bool(tamil_chars_re.search(text))

def test_google_transliteration_methods():
    """Test different Google transliteration approaches"""
    
    translator = Translator()
    
    # Test sentences
    test_sentences = [
        "அப்போது எனக்கு 27 வயதுதான்",
        "இன்று நல்ல நாள்",
        "வணக்கம், நான் தமிழ் பேசுகிறேன்",
        "நன்றி வணக்கம்"
    ]
    
    print("🧪 Testing Google Transliteration Methods:")
    print("=" * 80)
    
    for i, tamil_text in enumerate(test_sentences, 1):
        print(f"\nTest {i}: {tamil_text}")
        print("-" * 60)
        
        # Method 1: Direct translation (sometimes gives transliteration)
        try:
            direct = translator.translate(tamil_text, src='ta', dest='en')
            print(f"Direct (ta→en):     {direct.text}")
        except Exception as e:
            print(f"Direct (ta→en):     ERROR - {e}")
        
        # Method 2: Tamil → Hindi → English
        try:
            hindi = translator.translate(tamil_text, src='ta', dest='hi')
            hindi_to_en = translator.translate(hindi.text, src='hi', dest='en')
            print(f"Via Hindi:          {hindi_to_en.text}")
        except Exception as e:
            print(f"Via Hindi:          ERROR - {e}")
        
        # Method 3: Tamil → Telugu → English
        try:
            telugu = translator.translate(tamil_text, src='ta', dest='te')
            telugu_to_en = translator.translate(telugu.text, src='te', dest='en')
            print(f"Via Telugu:         {telugu_to_en.text}")
        except Exception as e:
            print(f"Via Telugu:         ERROR - {e}")
        
        # Method 4: Tamil → Kannada → English
        try:
            kannada = translator.translate(tamil_text, src='ta', dest='kn')
            kannada_to_en = translator.translate(kannada.text, src='kn', dest='en')
            print(f"Via Kannada:        {kannada_to_en.text}")
        except Exception as e:
            print(f"Via Kannada:        ERROR - {e}")
        
        # Method 5: Tamil → Malayalam → English
        try:
            malayalam = translator.translate(tamil_text, src='ta', dest='ml')
            malayalam_to_en = translator.translate(malayalam.text, src='ml', dest='en')
            print(f"Via Malayalam:      {malayalam_to_en.text}")
        except Exception as e:
            print(f"Via Malayalam:      ERROR - {e}")
    
    print("\n" + "=" * 80)
    print("Test completed!")
    print("\nNote: The method that gives the most phonetic/transliterated")
    print("result (rather than semantic translation) is the best for our use case.")

if __name__ == "__main__":
    test_google_transliteration_methods()
