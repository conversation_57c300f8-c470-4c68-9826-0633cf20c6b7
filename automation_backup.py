import os
import cv2
import numpy as np
import subprocess
import json
import time
import glob
from shutil import copy2
from tqdm import tqdm
from collections import defaultdict
from datetime import timedelta
import ffmpeg

# GPU imports
try:
    import torch
    import torch.nn.functional as F
    GPU_AVAILABLE = torch.cuda.is_available()
    DEVICE = 'cuda' if GPU_AVAILABLE else 'cpu'
    TORCH_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False
    DEVICE = 'cpu'
    TORCH_AVAILABLE = False


def format_time(seconds):
    return str(timedelta(seconds=int(seconds))).zfill(8)


def save_group_representative_images(groups, metadata, first_frames_dir, output_dir):
    os.makedirs(output_dir, exist_ok=True)
    metadata_dict = {clip["clip"]: clip for clip in metadata}

    for group in tqdm(groups, desc="📸 Saving group images"):
        if not group:
            continue

        group_clips = sorted([metadata_dict[c["clip"] if isinstance(c, dict) else c] for c in group], key=lambda x: x["clip_no"])
        first_clip = group_clips[0]

        image_path = os.path.join(first_frames_dir, first_clip["first_frame_image"])
        frame = cv2.imread(image_path)
        if frame is None:
            continue

        filename = f"clip{first_clip['clip_no']:04d}_group.jpg"
        cv2.putText(frame, f"Group {first_clip['clip_no']}", (10, 40), cv2.FONT_HERSHEY_SIMPLEX, 1.1, (0, 255, 0), 2)
        cv2.imwrite(os.path.join(output_dir, filename), frame)


def calculate_histogram_diff(frame1, frame2):
    hsv1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2HSV)
    hsv2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2HSV)
    hist1 = cv2.calcHist([hsv1], [0, 1], None, [50, 60], [0, 180, 0, 256])
    hist2 = cv2.calcHist([hsv2], [0, 1], None, [50, 60], [0, 180, 0, 256])
    cv2.normalize(hist1, hist1)
    cv2.normalize(hist2, hist2)
    return 1 - cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)

def extract_clip(input_video, start_time, duration, output_path):
    try:
        (
            ffmpeg
            .input(input_video, ss=start_time)
            .output(output_path, t=duration, map='0:v', c='copy', y=None)
            .overwrite_output()
            .run(quiet=True)
        )
    except ffmpeg.Error as e:
        print(f"❌ FFmpeg error extracting clip: {e.stderr.decode() if e.stderr else e}")

def extract_clip_original(input_video, start_time, duration, output_path):
    try:
        (
            ffmpeg
            .input(input_video, ss=start_time)
            .output(output_path, t=duration, c='copy', y=None)
            .overwrite_output()
            .run(quiet=True)
        )
    except ffmpeg.Error as e:
        print(f"❌ FFmpeg error extracting clip: {e.stderr.decode() if e.stderr else e}")

def save_first_frame(video_path, frame_number, output_path, clip_number, start_frame, end_frame, duration):
    cap = cv2.VideoCapture(video_path)
    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
    ret, frame = cap.read()
    if ret:
        font = cv2.FONT_HERSHEY_SIMPLEX
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        text_lines = [
            f"Clip {clip_number}",
            f"Start: {start_frame}",
            f"End: {end_frame}",
            f"Duration: {duration:.2f}s",
            f"Start Time: {start_frame / cap.get(cv2.CAP_PROP_FPS):.2f}s",
            f"Resolution: {width}x{height}"
        ]
        y0 = 40
        for i, line in enumerate(text_lines):
            y = y0 + i * 40
            cv2.putText(frame, line, (10, y), font, 1.2, (0, 255, 0), 3)
        cv2.imwrite(output_path, frame)
    cap.release()

def cluster_clips_by_histogram(all_metadata, first_frames_dir, cluster_similarity=0.15):
    """
    Clusters clips based on histogram similarity of their first frames.

    Args:
        all_metadata (list): List of clip metadata dicts.
        first_frames_dir (str): Path to folder with first frame images.
        cluster_similarity (float): Threshold for histogram distance to group clips.

    Returns:
        groups (list of lists): Each inner list contains clips grouped as a cluster.
    """
    groups = []
    histograms = []

    pbar = tqdm(all_metadata, desc="📊 Grouping clips", unit="clip")
    for clip in pbar:
        img_path = os.path.join(first_frames_dir, clip["first_frame_image"])
        img = cv2.imread(img_path)
        if img is None:
            continue

        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        hist = cv2.calcHist([hsv], [0, 1], None, [50, 60], [0, 180, 0, 256])
        cv2.normalize(hist, hist)

        placed = False
        for i, group_hist in enumerate(histograms):
            dist = 1 - cv2.compareHist(hist, group_hist, cv2.HISTCMP_CORREL)
            if dist < cluster_similarity:
                groups[i].append(clip)
                # Update group histogram as running average
                groups_count = len(groups[i])
                histograms[i] = (group_hist * (groups_count - 1) + hist) / groups_count
                placed = True
                break

        if not placed:
            groups.append([clip])
            histograms.append(hist)

    pbar.close()
    return groups

def process_video(video_path, clips_dir, first_frames_dir, clip_start_index, video_index, threshold=0.15, min_scene_gap=2):
    cap = cv2.VideoCapture(video_path)
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    prev_frame = None
    frame_id = 0
    last_scene_frame = 0
    clip_index = clip_start_index
    metadata = []

    video_id = f"vid{video_index + 1:04d}"
    print(f"\n🎮 Processing {os.path.basename(video_path)} ({video_id})")

    # Create progress bar
    pbar = tqdm(total=total_frames, desc="Processing frames", unit="frame")

    while True:
        ret, frame = cap.read()
        if not ret:
            break

        if prev_frame is not None:
            diff = calculate_histogram_diff(prev_frame, frame)
            if diff > threshold and (frame_id - last_scene_frame) > min_scene_gap:
                print("\ndiff: ", diff)
                start_time = last_scene_frame / fps
                duration = (frame_id - last_scene_frame) / fps
                ext = os.path.splitext(video_path)[-1].lower()
                clip_name = f"clip_{clip_index:04d}_{last_scene_frame:06d}_{frame_id:06d}_{duration:.2f}s_{start_time:.2f}s_{width}x{height}_{video_id}{ext}"
                clip_path = os.path.join(clips_dir, clip_name)

                extract_clip(video_path, start_time, duration, clip_path)
                pbar.write(f"✅ Extracted: {clip_name}")

                img_name = clip_name.replace(ext, ".jpg")
                img_path = os.path.join(first_frames_dir, img_name)
                save_first_frame(video_path, last_scene_frame, img_path, clip_index, last_scene_frame, frame_id, duration)

                metadata.append({
                    "clip": clip_name,
                    "clip_no": clip_index,
                    "video_id": video_id,
                    "source_video": os.path.basename(video_path),
                    "start_frame": last_scene_frame,
                    "end_frame": frame_id,
                    "start_time": round(start_time, 3),
                    "duration": round(duration, 3),
                    "first_frame_image": img_name
                })

                clip_index += 1
                last_scene_frame = frame_id

        prev_frame = frame
        frame_id += 1
        pbar.update(1)

    if frame_id > last_scene_frame:
        start_time = last_scene_frame / fps
        duration = (frame_id - last_scene_frame) / fps
        ext = os.path.splitext(video_path)[-1].lower()
        clip_name = f"clip_{clip_index:04d}_{last_scene_frame:06d}_{frame_id:06d}_{duration:.2f}s_{start_time:.2f}s_{width}x{height}_{video_id}{ext}"
        clip_path = os.path.join(clips_dir, clip_name)

        extract_clip(video_path, start_time, duration, clip_path)
        pbar.write(f"✅ Extracted: {clip_name}")

        img_name = clip_name.replace(ext, ".jpg")
        img_path = os.path.join(first_frames_dir, img_name)
        save_first_frame(video_path, last_scene_frame, img_path, clip_index, last_scene_frame, frame_id, duration)

        metadata.append({
            "clip": clip_name,
            "clip_no": clip_index,
            "video_id": video_id,
            "source_video": os.path.basename(video_path),
            "start_frame": last_scene_frame,
            "end_frame": frame_id,
            "start_time": round(start_time, 3),
            "duration": round(duration, 3),
            "first_frame_image": img_name
        })

        clip_index += 1

    pbar.close()
    cap.release()
    return metadata, clip_index

def task2(clips_dir):
    parent_dir = os.path.abspath(os.path.join(clips_dir, os.pardir))
    groups_path = os.path.join(clips_dir, "groups.json")

    if not os.path.exists(groups_path):
        print("❌ groups.json not found. Run clustering first.")
        return

    with open(groups_path, "r") as f:
        groups = json.load(f)

    clip_input = input("\n🎯 Enter clip number or 'exit': ").strip()
    if clip_input.lower() == 'exit':
        return

    try:
        clip_number = int(clip_input)
        matched_group = next((group for group in groups if any(clip["clip_no"] == clip_number for clip in group)), None)

        if matched_group:
            clip_folder = os.path.join(parent_dir, f"clip_{clip_number:04d}")
            shots_dir = os.path.join(clip_folder, "shots")
            os.makedirs(shots_dir, exist_ok=True)

            for clip in tqdm(matched_group, desc="Copying clips"):
                source_clip_path = os.path.join(clips_dir, clip["clip"])
                if os.path.exists(source_clip_path):
                    copy2(source_clip_path, shots_dir)
        else:
            print("⚠️ Clip not found.")
    except ValueError:
        print("⚠️ Invalid input.")


def task3(clips_root):
    for folder_name in sorted(os.listdir(clips_root)):
        clip_path = os.path.join(clips_root, folder_name)
        if not os.path.isdir(clip_path) or not folder_name.startswith("clip_"):
            continue

        mid_dir = os.path.join(clip_path, "mov", "mid")
        out_dir = os.path.join(clip_path, "mov", "out")
        os.makedirs(out_dir, exist_ok=True)

        if os.path.exists(mid_dir):
            for file_name in os.listdir(mid_dir):
                if file_name.lower().endswith((".mov", ".mp4")):
                    mid_file = os.path.join(mid_dir, file_name)
                    output_file = os.path.join(out_dir, file_name)
                    subprocess.run(["ffmpeg", "-y", "-i", mid_file, "-c", "copy", output_file], capture_output=True)


def task4(parent_dir):
    clips_dir = os.path.join(parent_dir, "clips")
    clip_folders = [f for f in sorted(os.listdir(parent_dir)) if os.path.isdir(os.path.join(parent_dir, f)) and f.startswith("clip_")]

    for folder_name in tqdm(clip_folders, desc="Merging folders"):
        out_mov_dir = os.path.join(parent_dir, folder_name, "mov", "out")
        if os.path.exists(out_mov_dir):
            merged_out_dir = os.path.join(parent_dir, folder_name, "out")
            os.makedirs(merged_out_dir, exist_ok=True)

            videos = [f for f in os.listdir(out_mov_dir) if f.lower().endswith((".mov", ".mp4"))]
            if videos:
                temp_list = os.path.join(merged_out_dir, "concat_list.txt")
                with open(temp_list, "w") as f:
                    for video in sorted(videos):
                        f.write(f"file '{os.path.join(out_mov_dir, video)}'\n")

                output_path = os.path.join(merged_out_dir, f"{folder_name}_merged.mp4")
                subprocess.run(["ffmpeg", "-y", "-f", "concat", "-safe", "0", "-i", temp_list, "-c", "copy", output_path], capture_output=True)



def task5(parent_dir):
    merged_out_dir = os.path.join(parent_dir, "out")
    os.makedirs(merged_out_dir, exist_ok=True)

    video_clips = defaultdict(list)

    # Collect all video clips
    for root, dirs, files in os.walk(parent_dir):
        for file in files:
            if file.lower().endswith((".mov", ".mp4")) and "vid" in file:
                parts = file.split("_")
                for part in parts:
                    if part.startswith("vid") and len(part) >= 7:
                        vid_id = part[:7]
                        video_clips[vid_id].append(os.path.join(root, file))

    # Merge each video ID
    for vid_id, clips in tqdm(video_clips.items(), desc="Merging videos"):
        if clips:
            temp_list = os.path.join(merged_out_dir, f"concat_{vid_id}.txt")
            with open(temp_list, "w") as f:
                for clip in sorted(clips):
                    f.write(f"file '{clip}'\n")

            output_path = os.path.join(merged_out_dir, f"{vid_id}.mp4")
            subprocess.run(["ffmpeg", "-y", "-f", "concat", "-safe", "0", "-i", temp_list, "-c", "copy", output_path], capture_output=True)


def task6(parent_dir):
    clip_input = input("\n🎯 Enter clip folder number or 'exit': ").strip()
    if clip_input.lower() == 'exit':
        return

    try:
        clip_number = int(clip_input)
        clip_folder = f"clip_{clip_number:04d}"
        clip_path = os.path.join(parent_dir, clip_folder)

        if os.path.exists(clip_path):
            out_mov_dir = os.path.join(clip_path, "mov", "out")
            if os.path.exists(out_mov_dir):
                merged_out_dir = os.path.join(clip_path, "out")
                os.makedirs(merged_out_dir, exist_ok=True)

                videos = [f for f in os.listdir(out_mov_dir) if f.lower().endswith((".mov", ".mp4"))]
                if videos:
                    temp_list = os.path.join(merged_out_dir, "concat_list.txt")
                    with open(temp_list, "w") as f:
                        for video in sorted(videos):
                            f.write(f"file '{os.path.join(out_mov_dir, video)}'\n")

                    output_path = os.path.join(merged_out_dir, f"{clip_folder}_merged.mp4")
                    subprocess.run(["ffmpeg", "-y", "-f", "concat", "-safe", "0", "-i", temp_list, "-c", "copy", output_path], capture_output=True)
                    print(f"✅ Merged: {output_path}")
        else:
            print("⚠️ Clip folder not found.")
    except ValueError:
        print("⚠️ Invalid input.")

def gpu_process_frame(frame, overlay, pad=20):
    """GPU-accelerated frame processing"""
    if not GPU_AVAILABLE:
        return cpu_process_frame(frame, overlay, pad)

    try:
        h, w = frame.shape[:2]

        # Convert to tensor and add alpha
        frame_tensor = torch.from_numpy(frame).float().to(DEVICE)
        alpha = torch.full((h, w, 1), 255.0, device=DEVICE)
        frame_bgra = torch.cat([frame_tensor, alpha], dim=2)

        # Add padding
        padded = F.pad(frame_bgra.permute(2, 0, 1), (pad, pad, pad, pad)).permute(1, 2, 0)

        # Resize overlay and blend
        overlay_tensor = torch.from_numpy(overlay).float().to(DEVICE)
        if overlay_tensor.shape[:2] != padded.shape[:2]:
            overlay_tensor = F.interpolate(
                overlay_tensor.permute(2, 0, 1).unsqueeze(0),
                size=padded.shape[:2], mode='bilinear'
            ).squeeze(0).permute(1, 2, 0)

        # Alpha blending
        alpha_overlay = overlay_tensor[:, :, 3:4] / 255.0
        result = padded.clone()
        result[:, :, :3] = (1 - alpha_overlay) * padded[:, :, :3] + alpha_overlay * overlay_tensor[:, :, :3]

        return result[:, :, :3].cpu().numpy().astype(np.uint8)
    except Exception:
        return cpu_process_frame(frame, overlay, pad)

def cpu_process_frame(frame, overlay, pad=20):
    """CPU fallback processing"""
    h, w = frame.shape[:2]
    padded = np.zeros((h + pad * 2, w + pad * 2, 3), dtype=np.uint8)
    padded[pad:pad + h, pad:pad + w] = frame

    # Simple overlay
    overlay_resized = cv2.resize(overlay, (padded.shape[1], padded.shape[0]))
    if overlay_resized.shape[2] == 4:
        alpha = overlay_resized[:, :, 3:4] / 255.0
        for c in range(3):
            padded[:, :, c] = padded[:, :, c] * (1 - alpha[:, :, 0]) + overlay_resized[:, :, c] * alpha[:, :, 0]

    return padded

def is_cuda_available():
    """Legacy function for compatibility"""
    return GPU_AVAILABLE



def add_padding_with_transparency_torch(frame, pad=20, device='cuda'):
    """PyTorch GPU-accelerated padding with transparency"""
    if not TORCH_AVAILABLE or not torch.cuda.is_available():
        return add_padding_with_transparency_cpu(frame, pad)

    try:
        # Convert to torch tensor and move to GPU
        if len(frame.shape) == 3 and frame.shape[2] == 3:
            # Add alpha channel
            alpha = np.full((frame.shape[0], frame.shape[1], 1), 255, dtype=np.uint8)
            frame = np.concatenate([frame, alpha], axis=2)

        # Convert to tensor (H, W, C) -> (C, H, W)
        frame_tensor = torch.from_numpy(frame).permute(2, 0, 1).float().to(device)

        # Add padding
        padded = F.pad(frame_tensor, (pad, pad, pad, pad), mode='constant', value=0)

        # Convert back to numpy (C, H, W) -> (H, W, C)
        result = padded.permute(1, 2, 0).cpu().numpy().astype(np.uint8)
        return result

    except Exception as e:
        print(f"PyTorch padding failed, using CPU: {e}")
        return add_padding_with_transparency_cpu(frame, pad)

def add_padding_with_transparency_cpu(frame, pad=20):
    """CPU fallback for padding"""
    h, w = frame.shape[:2]

    # Add alpha channel if needed
    if frame.shape[2] == 3:
        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2BGRA)

    # Efficient padding using numpy
    new_frame = np.zeros((h + pad * 2, w + pad * 2, 4), dtype=np.uint8)
    new_frame[pad:pad + h, pad:pad + w] = frame
    return new_frame

def add_padding_with_transparency(frame, pad=20, use_cuda=False):
    """Main padding function with GPU/CPU selection"""
    if use_cuda and TORCH_AVAILABLE and torch.cuda.is_available():
        return add_padding_with_transparency_torch(frame, pad)
    else:
        return add_padding_with_transparency_cpu(frame, pad)

def overlay_image_alpha_torch(background, overlay, device='cuda'):
    """PyTorch GPU-accelerated alpha blending"""
    if not TORCH_AVAILABLE or not torch.cuda.is_available():
        return overlay_image_alpha_cpu(background, overlay)

    try:
        # Ensure both images have alpha channels
        if background.shape[2] == 3:
            alpha_bg = np.full((background.shape[0], background.shape[1], 1), 255, dtype=np.uint8)
            background = np.concatenate([background, alpha_bg], axis=2)

        if overlay.shape[2] == 3:
            alpha_ov = np.full((overlay.shape[0], overlay.shape[1], 1), 255, dtype=np.uint8)
            overlay = np.concatenate([overlay, alpha_ov], axis=2)

        # Resize overlay if needed
        if overlay.shape[:2] != background.shape[:2]:
            overlay = cv2.resize(overlay, (background.shape[1], background.shape[0]))

        # Convert to torch tensors and move to GPU
        bg_tensor = torch.from_numpy(background).float().to(device) / 255.0
        ov_tensor = torch.from_numpy(overlay).float().to(device) / 255.0

        # Extract alpha channels
        alpha_overlay = ov_tensor[:, :, 3:4]
        alpha_background = 1.0 - alpha_overlay

        # Alpha blending
        result_tensor = torch.zeros_like(bg_tensor)
        result_tensor[:, :, :3] = (alpha_overlay * ov_tensor[:, :, :3] +
                                  alpha_background * bg_tensor[:, :, :3])
        result_tensor[:, :, 3] = bg_tensor[:, :, 3]  # Keep background alpha

        # Convert back to numpy
        result = (result_tensor * 255.0).cpu().numpy().astype(np.uint8)
        return result

    except Exception as e:
        print(f"PyTorch alpha blending failed, using CPU: {e}")
        return overlay_image_alpha_cpu(background, overlay)

def overlay_image_alpha_cpu(background, overlay):
    """CPU fallback for alpha blending"""
    # Resize overlay to match background if needed
    if overlay.shape[:2] != background.shape[:2]:
        overlay = cv2.resize(overlay, (background.shape[1], background.shape[0]))

    # Ensure overlay has alpha channel
    if overlay.shape[2] != 4:
        overlay = cv2.cvtColor(overlay, cv2.COLOR_BGR2BGRA)

    # Ensure background has alpha channel
    if background.shape[2] != 4:
        background = cv2.cvtColor(background, cv2.COLOR_BGR2BGRA)

    # Optimized alpha blending using numpy vectorization
    alpha_overlay = overlay[:, :, 3:4].astype(np.float32) / 255.0
    alpha_background = 1.0 - alpha_overlay

    # Vectorized blending for all color channels at once
    overlay_rgb = overlay[:, :, :3].astype(np.float32)
    background_rgb = background[:, :, :3].astype(np.float32)

    result = np.zeros_like(background, dtype=np.uint8)
    result[:, :, :3] = (alpha_overlay * overlay_rgb + alpha_background * background_rgb).astype(np.uint8)
    result[:, :, 3] = background[:, :, 3]  # retain original alpha

    return result

def overlay_image_alpha(background, overlay, use_cuda=False):
    """Main alpha blending function with GPU/CPU selection"""
    if use_cuda and TORCH_AVAILABLE and torch.cuda.is_available():
        return overlay_image_alpha_torch(background, overlay)
    else:
        return overlay_image_alpha_cpu(background, overlay)

def process_video_overlay(video_path, overlay_img, output_path, use_cuda=False):
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"⚠️ Couldn't open video: {video_path}")
        return

    # Use H.264 codec for better compatibility and performance
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    fps = cap.get(cv2.CAP_PROP_FPS)
    w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    out_w, out_h = w + 40, h + 40
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    out = cv2.VideoWriter(output_path, fourcc, fps, (out_w, out_h), True)
    if not out.isOpened():
        print(f"⚠️ Couldn't write video: {output_path}")
        return

    print(f"📊 Processing {total_frames} frames at {fps:.2f} FPS")
    print(f"📐 Input: {w}x{h} → Output: {out_w}x{out_h}")

    # Pre-process overlay to match output size for efficiency
    overlay_resized = cv2.resize(overlay_img, (out_w, out_h))
    if overlay_resized.shape[2] != 4:
        overlay_resized = cv2.cvtColor(overlay_resized, cv2.COLOR_BGR2BGRA)

    # Pre-compute alpha values for faster blending
    alpha_overlay = overlay_resized[:, :, 3:4].astype(np.float32) / 255.0
    alpha_background = 1.0 - alpha_overlay
    overlay_rgb = overlay_resized[:, :, :3].astype(np.float32)

    pbar = tqdm(total=total_frames, desc=os.path.basename(video_path), unit="frames")
    frame_count = 0
    processing_start = time.time()

    while True:
        ret, frame = cap.read()
        if not ret:
            break

        if use_cuda:
            try:
                # GPU processing
                gpu_frame = cv2.cuda_GpuMat()
                gpu_frame.upload(frame)
                gpu_frame_bgra = cv2.cuda.cvtColor(gpu_frame, cv2.COLOR_BGR2BGRA)
                frame = gpu_frame_bgra.download()
            except Exception:
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2BGRA)
        else:
            # Optimized CPU processing - direct padding without intermediate conversion
            padded_frame = np.zeros((out_h, out_w, 3), dtype=np.uint8)
            padded_frame[20:20+h, 20:20+w] = frame

            # Fast alpha blending without BGRA conversion
            padded_float = padded_frame.astype(np.float32)
            result_rgb = alpha_background * padded_float + alpha_overlay * overlay_rgb
            result_frame = result_rgb.astype(np.uint8)

            out.write(result_frame)
            frame_count += 1
            pbar.update(1)

            # Performance monitoring
            if frame_count % 100 == 0:
                elapsed = time.time() - processing_start
                current_fps = frame_count / elapsed if elapsed > 0 else 0
                pbar.set_postfix({"FPS": f"{current_fps:.1f}"})

            continue

        # GPU path (if CUDA is available)
        padded_frame = add_padding_with_transparency(frame, use_cuda=use_cuda)
        result = overlay_image_alpha(padded_frame, overlay_resized, use_cuda=use_cuda)

        if use_cuda:
            try:
                gpu_result = cv2.cuda_GpuMat()
                gpu_result.upload(result)
                gpu_result_bgr = cv2.cuda.cvtColor(gpu_result, cv2.COLOR_BGRA2BGR)
                result_bgr = gpu_result_bgr.download()
            except Exception:
                result_bgr = cv2.cvtColor(result, cv2.COLOR_BGRA2BGR)
        else:
            result_bgr = cv2.cvtColor(result, cv2.COLOR_BGRA2BGR)

        out.write(result_bgr)
        frame_count += 1
        pbar.update(1)

        if frame_count % 100 == 0:
            elapsed = time.time() - processing_start
            current_fps = frame_count / elapsed if elapsed > 0 else 0
            pbar.set_postfix({"FPS": f"{current_fps:.1f}"})

    pbar.close()
    cap.release()
    out.release()

    total_time = time.time() - processing_start
    avg_fps = frame_count / total_time if total_time > 0 else 0
    print(f"✅ Processed {frame_count} frames in {total_time:.1f}s (avg {avg_fps:.1f} FPS)")


def main():
    all_metadata = []
    parent_dir = None

    while True:
        original_shots_path = input("\n📁 Enter path to 'original_shots': ").strip()
        if os.path.isdir(original_shots_path):
            parent_dir = os.path.abspath(os.path.join(original_shots_path, os.pardir))
            break
        else:
            print("❌ Invalid folder path. Try again.")

    while True:
        print("\nChoose a task:\n"
              "1: Extract clips from videos\n"
              "2: Organize selected clips\n"
              "3: Important: Format correction\n"
              "4: Merge product wise\n"
              "5: Final merge all in one\n"
              "6: Merge clip folder wise\n"
              "7: cut orginal with everything\n"
              "8: Overlay and padding\n"
              "exit: Exit the tool")
        choice = input("Enter your choice: ").strip()

        if choice == '1':
            start_time = time.time()
            clips_dir = os.path.join(parent_dir, "clips")
            first_frames_dir = os.path.join(parent_dir, "shots_first_frames")
            os.makedirs(clips_dir, exist_ok=True)
            os.makedirs(first_frames_dir, exist_ok=True)

            video_files = sorted([
                f for f in os.listdir(original_shots_path)
                if os.path.isfile(os.path.join(original_shots_path, f)) and f.lower().endswith(('.mp4', '.mov'))
            ])

            all_metadata = []
            video_id_map = {}  # ← NEW: video_id → original video filename
            global_clip_index = 1

            for vid_index, video_file in enumerate(video_files):
                video_path = os.path.join(original_shots_path, video_file)
                metadata, global_clip_index = process_video(
                    video_path, clips_dir, first_frames_dir, global_clip_index, vid_index)
                all_metadata.extend(metadata)

                # Add mapping for this video_id
                video_id = f"vid{vid_index+1:04d}"
                video_id_map[video_id] = video_file

            metadata_path = os.path.join(clips_dir, "clips_metadata.json")
            with open(metadata_path, "w") as f:
                json.dump(all_metadata, f, indent=2)

            # Save the video_id to filename map
            video_id_map_path = os.path.join(clips_dir, "video_id_map.json")
            with open(video_id_map_path, "w") as f:
                json.dump(video_id_map, f, indent=2)

            print("🔍 Clustering all clips from all videos...")
            groups = cluster_clips_by_histogram(all_metadata, first_frames_dir)
            save_group_representative_images(
                groups=groups,
                metadata=all_metadata,
                first_frames_dir=os.path.join(parent_dir, "shots_first_frames"),
                output_dir=os.path.join(parent_dir, "clustered_first_frames")
            )

            print(f"✅ Total clusters formed: {len(groups)}")

            combined_json_path = os.path.join(clips_dir, "all_clips_metadata_and_clusters.json")
            with open(combined_json_path, "w") as f:
                json.dump({
                    "metadata": all_metadata,
                    "clusters": groups,
                    "video_id_map": video_id_map
                }, f, indent=4)

            # ✅ Save just groups for Task 2
            groups_json_path = os.path.join(clips_dir, "groups.json")
            with open(groups_json_path, "w") as f:
                json.dump(groups, f, indent=2)

            print(f"\n📦 Total clips: {len(all_metadata)}")
            print(f"🖼️ First frames saved in: {first_frames_dir}")
            print(f"📜 Metadata saved at: {metadata_path}")
            print(f"🗺️ Video ID Map saved at: {video_id_map_path}")
            print(f"🧩 Cluster groups saved at: {groups_json_path}")
            print(f"⏱️ Time taken: {time.time() - start_time:.2f} seconds")
            print("✅ All done!")

        elif choice == '2':
            start_time = time.time()
            clips_dir = os.path.join(parent_dir, "clips")

            task2(clips_dir)

            print(f"⏱️ Time taken: {time.time() - start_time:.2f} seconds")
            print("✅ All done!")

        elif choice == '3':
            start_time = time.time()

            task3(parent_dir)

            print(f"⏱️ Time taken: {time.time() - start_time:.2f} seconds")
            print("✅ All done!")

        elif choice == '4':
            start_time = time.time()

            task4(parent_dir)

            print(f"⏱️ Time taken: {time.time() - start_time:.2f} seconds")
            print("✅ All done!")

        elif choice == '5':
            start_time = time.time()

            task5(parent_dir)

            print(f"⏱️ Time taken: {time.time() - start_time:.2f} seconds")
            print("✅ All done!")
        
        elif choice == '6':
            start_time = time.time()

            task6(parent_dir)

            print(f"⏱️ Time taken: {time.time() - start_time:.2f} seconds")
            print("✅ All done!")

        elif choice == '7':
            start_time = float(input("Start second (e.g. 0): "))
            duration = float(input("Duration (e.g. 10): "))
            input_video = input("Input video path with name: ")
            output_path = input("Output path with name: ")
            extract_clip_original(input_video, start_time, duration, output_path)

        elif choice == '8':
            # Use the provided test video path
            test_video_path = r"D:\testing\marumagal\original_video"

            if not os.path.exists(test_video_path):
                print(f"❌ Test video path not found: {test_video_path}")
                print("Please ensure the path exists and contains video files.")
                continue

            input_dir = os.path.abspath(test_video_path)
            overlay_path = os.path.join(os.path.dirname(__file__), "suntv_logo.png")
            output_dir = os.path.join(input_dir, "overlayed_logo")

            if not os.path.exists(overlay_path):
                print("❌ Overlay image 'suntv_logo.png' not found in script folder.")
                print("Creating a sample overlay image for testing...")
                # Create a simple test overlay image
                test_overlay = np.zeros((100, 200, 4), dtype=np.uint8)
                test_overlay[:, :, 0] = 255  # Blue channel
                test_overlay[:, :, 3] = 128  # Alpha channel (semi-transparent)
                cv2.putText(test_overlay, "TEST LOGO", (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255, 255), 2)
                cv2.imwrite(overlay_path, test_overlay)
                print(f"✅ Created test overlay at: {overlay_path}")

            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            overlay_img = cv2.imread(overlay_path, cv2.IMREAD_UNCHANGED)
            if overlay_img is None:
                print("❌ Failed to load overlay image.")
                continue
            if overlay_img.shape[2] != 4:
                print("⚠️ Overlay image doesn't have alpha channel, adding one...")
                overlay_img = cv2.cvtColor(overlay_img, cv2.COLOR_BGR2BGRA)

            # GPU processing
            use_cuda = GPU_AVAILABLE

            # Setup processing mode
            if gpu_info['torch_cuda']:
                print("🚀 Using PyTorch GPU acceleration!")
                print(f"   Device: {gpu_info['device_name']}")
                processing_mode = "PyTorch GPU"
            elif gpu_info['opencv_cuda']:
                print("� Using OpenCV CUDA acceleration!")
                processing_mode = "OpenCV CUDA"
            else:
                print("🔧 Using optimized CPU processing...")
                # Set optimal number of threads
                num_threads = min(cv2.getNumberOfCPUs(), 8)
                cv2.setNumThreads(num_threads)
                print(f"   Using {num_threads} CPU threads")

                # Enable optimized CPU instructions
                try:
                    cv2.setUseOptimized(True)
                    print("   CPU optimizations enabled")
                except:
                    pass
                processing_mode = "CPU Optimized"

            print(f"⚙️  Processing mode: {processing_mode}")

            videos = glob.glob(os.path.join(input_dir, "*.mp4")) + glob.glob(os.path.join(input_dir, "*.mov"))

            if not videos:
                print(f"❌ No video files found in: {input_dir}")
                print("Supported formats: .mp4, .mov")
                continue

            print(f"📹 Found {len(videos)} video(s) to process")

            # Process videos with timing
            start_time = time.time()
            for i, video_path in enumerate(videos):
                filename = os.path.basename(video_path)
                output_path = os.path.join(output_dir, filename)
                print(f"\n🎬 Processing ({i+1}/{len(videos)}): {filename}")

                video_start = time.time()
                process_video_overlay(video_path, overlay_img, output_path, use_cuda)
                video_time = time.time() - video_start

                print(f"✅ Completed: {filename} (took {video_time:.1f}s)")

            total_time = time.time() - start_time
            print(f"\n🎉 All videos processed in {total_time:.1f}s!")
            print(f"📁 Output saved to: {output_dir}")

            if not use_cuda:
                print("\n💡 To enable GPU acceleration:")
                print("   1. Install PyTorch with CUDA: pip install torch torchvision --index-url https://download.pytorch.org/whl/cu121")
                print("   2. Or install OpenCV with CUDA support")
                print("   3. Current CPU processing is already optimized!")

        elif choice.lower() == 'exit':
            print("👋 Exiting the tool.")
            break

        else:
            print("❌ Invalid choice. Please enter 1, 2, 3, 4 or 'exit'.")

if __name__ == "__main__":
    main()
