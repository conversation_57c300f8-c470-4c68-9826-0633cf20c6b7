import subprocess
import sys
import os
import urllib.request
import zipfile
import shutil
from pathlib import Path

def run_command(cmd, description=""):
    """Run a command and return success status"""
    print(f"🔧 {description}")
    print(f"Running: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
    try:
        if isinstance(cmd, str):
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        else:
            result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ Success: {description}")
            if result.stdout.strip():
                print(f"Output: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ Failed: {description}")
            if result.stderr.strip():
                print(f"Error: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def check_nvidia_driver():
    """Check NVIDIA driver version"""
    print("\n🔍 Checking NVIDIA Driver...")
    if run_command("nvidia-smi", "Checking NVIDIA driver"):
        return True
    else:
        print("❌ NVIDIA driver not found or not working")
        print("Please install NVIDIA drivers from: https://www.nvidia.com/drivers")
        return False

def install_cuda_toolkit():
    """Install CUDA Toolkit"""
    print("\n🚀 Installing CUDA Toolkit...")
    
    # CUDA 12.3 is compatible with RTX 4060
    cuda_url = "https://developer.download.nvidia.com/compute/cuda/12.3.2/local_installers/cuda_12.3.2_546.12_windows.exe"
    cuda_installer = "cuda_installer.exe"
    
    print("📥 Downloading CUDA Toolkit (this may take a while)...")
    try:
        urllib.request.urlretrieve(cuda_url, cuda_installer)
        print("✅ CUDA Toolkit downloaded")
    except Exception as e:
        print(f"❌ Failed to download CUDA: {e}")
        print("Please manually download from: https://developer.nvidia.com/cuda-downloads")
        return False
    
    print("🔧 Installing CUDA Toolkit...")
    print("⚠️ This will open an installer window - follow the prompts")
    
    # Run installer
    try:
        subprocess.run([cuda_installer], check=False)
        print("✅ CUDA installer launched")
        print("Please complete the installation and press Enter when done...")
        input()
        
        # Clean up
        if os.path.exists(cuda_installer):
            os.remove(cuda_installer)
        
        return True
    except Exception as e:
        print(f"❌ Failed to run CUDA installer: {e}")
        return False

def install_cudnn():
    """Install cuDNN"""
    print("\n🧠 Installing cuDNN...")
    
    print("📝 cuDNN requires manual download due to NVIDIA's terms.")
    print("1. Go to: https://developer.nvidia.com/cudnn")
    print("2. Create/login to NVIDIA account")
    print("3. Download cuDNN for CUDA 12.x")
    print("4. Extract to C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.3\\")
    
    response = input("Have you downloaded and extracted cuDNN? (y/n): ")
    return response.lower() == 'y'

def setup_environment_variables():
    """Setup CUDA environment variables"""
    print("\n🔧 Setting up environment variables...")
    
    cuda_path = "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.3"
    
    # Add to PATH
    paths_to_add = [
        f"{cuda_path}\\bin",
        f"{cuda_path}\\libnvvp",
        f"{cuda_path}\\extras\\CUPTI\\lib64"
    ]
    
    current_path = os.environ.get('PATH', '')
    
    for path in paths_to_add:
        if path not in current_path:
            print(f"Adding to PATH: {path}")
            # Note: This only affects current session
            os.environ['PATH'] = f"{path};{current_path}"
    
    # Set CUDA_PATH
    os.environ['CUDA_PATH'] = cuda_path
    print(f"Set CUDA_PATH: {cuda_path}")
    
    print("⚠️ Environment variables set for current session only")
    print("For permanent setup, add these to System Environment Variables:")
    for path in paths_to_add:
        print(f"  PATH: {path}")
    print(f"  CUDA_PATH: {cuda_path}")
    
    return True

def install_opencv_cuda():
    """Install OpenCV with CUDA support"""
    print("\n📷 Installing OpenCV with CUDA support...")
    
    # First try conda approach
    if shutil.which("conda"):
        print("🐍 Found conda, using conda-forge...")
        commands = [
            "conda create -n cuda_env python=3.11 -y",
            "conda activate cuda_env",
            "conda install -c conda-forge opencv -y",
            "conda install -c conda-forge ffmpeg tqdm numpy -y"
        ]
        
        for cmd in commands:
            if not run_command(cmd, f"Running: {cmd}"):
                print("⚠️ Conda installation had issues, trying pip...")
                break
        else:
            print("✅ Conda installation completed")
            print("To use: conda activate cuda_env")
            return True
    
    # Fallback to pip with pre-built wheels
    print("📦 Installing with pip...")
    
    commands = [
        [sys.executable, "-m", "pip", "uninstall", "opencv-python", "opencv-contrib-python", "-y"],
        [sys.executable, "-m", "pip", "install", "--upgrade", "pip"],
        [sys.executable, "-m", "pip", "install", "numpy<2"],
        [sys.executable, "-m", "pip", "install", "opencv-contrib-python==********"],
        [sys.executable, "-m", "pip", "install", "tqdm", "ffmpeg-python"]
    ]
    
    for cmd in commands:
        run_command(cmd, f"Installing packages")
    
    return True

def test_cuda_installation():
    """Test if CUDA is working"""
    print("\n🧪 Testing CUDA installation...")
    
    # Test CUDA toolkit
    if run_command("nvcc --version", "Testing CUDA compiler"):
        print("✅ CUDA toolkit working")
    else:
        print("❌ CUDA toolkit not found")
        return False
    
    # Test OpenCV CUDA
    test_script = '''
import cv2
import numpy as np

print(f"OpenCV version: {cv2.__version__}")

# Check build info
build_info = cv2.getBuildInformation()
has_cuda = "CUDA" in build_info
print(f"CUDA in build: {has_cuda}")

if has_cuda:
    try:
        device_count = cv2.cuda.getCudaEnabledDeviceCount()
        print(f"CUDA devices: {device_count}")
        
        if device_count > 0:
            # Test basic GPU operation
            test_img = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
            gpu_img = cv2.cuda_GpuMat()
            gpu_img.upload(test_img)
            gpu_gray = cv2.cuda.cvtColor(gpu_img, cv2.COLOR_BGR2GRAY)
            result = gpu_gray.download()
            print("✅ GPU operations working!")
        else:
            print("❌ No CUDA devices detected")
    except Exception as e:
        print(f"❌ CUDA operations failed: {e}")
else:
    print("❌ OpenCV not compiled with CUDA")
'''
    
    try:
        with open("test_cuda.py", "w") as f:
            f.write(test_script)
        
        result = subprocess.run([sys.executable, "test_cuda.py"], 
                              capture_output=True, text=True)
        print("CUDA Test Results:")
        print(result.stdout)
        if result.stderr:
            print("Errors:")
            print(result.stderr)
        
        os.remove("test_cuda.py")
        
        return "GPU operations working!" in result.stdout
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def create_gpu_test_script():
    """Create a test script for the automation"""
    script_content = '''
import os
import sys
sys.path.append(r"C:\\Users\\<USER>\\scripts")

# Test the automation script with GPU
from automation import is_cuda_available, process_video
import cv2
import numpy as np
import time

def test_gpu_automation():
    print("🚀 Testing GPU Automation Script")
    
    # Check GPU
    gpu_available = is_cuda_available()
    print(f"GPU Available: {gpu_available}")
    
    if gpu_available:
        print("✅ GPU acceleration ready!")
        print("Your automation script will now use GPU acceleration.")
        print("Expected performance: 50-100 FPS vs 10-15 FPS CPU")
    else:
        print("❌ GPU not available, using optimized CPU")
    
    # Test video path
    test_path = r"D:\\testing\\marumagal\\original_video"
    if os.path.exists(test_path):
        print(f"✅ Test video path found: {test_path}")
    else:
        print(f"❌ Test video path not found: {test_path}")
    
    print("\\n🎬 Ready to run automation.py with choice 8!")

if __name__ == "__main__":
    test_gpu_automation()
'''
    
    with open("test_gpu_automation.py", "w") as f:
        f.write(script_content)
    
    print("✅ Created test script: test_gpu_automation.py")

def main():
    print("🚀 Complete CUDA Setup for RTX 4060")
    print("=" * 50)
    
    # Step 1: Check NVIDIA driver
    if not check_nvidia_driver():
        print("\n❌ Please install NVIDIA drivers first and restart")
        return
    
    # Step 2: Install CUDA Toolkit
    print("\n" + "=" * 50)
    response = input("Install CUDA Toolkit? (y/n): ")
    if response.lower() == 'y':
        install_cuda_toolkit()
    
    # Step 3: Setup environment
    setup_environment_variables()
    
    # Step 4: Install cuDNN
    print("\n" + "=" * 50)
    install_cudnn()
    
    # Step 5: Install OpenCV with CUDA
    print("\n" + "=" * 50)
    install_opencv_cuda()
    
    # Step 6: Test installation
    print("\n" + "=" * 50)
    if test_cuda_installation():
        print("\n🎉 SUCCESS! CUDA is working!")
        create_gpu_test_script()
        print("\n🎯 Next steps:")
        print("1. Run: python test_gpu_automation.py")
        print("2. Run: python automation.py (choice 8)")
        print("3. Enjoy GPU acceleration!")
    else:
        print("\n❌ CUDA setup incomplete")
        print("You may need to:")
        print("1. Restart your computer")
        print("2. Check environment variables")
        print("3. Reinstall components")
    
    print("\n📝 Manual installation links:")
    print("CUDA: https://developer.nvidia.com/cuda-downloads")
    print("cuDNN: https://developer.nvidia.com/cudnn")
    print("Drivers: https://www.nvidia.com/drivers")

if __name__ == "__main__":
    main()
