def frames_to_timecode(fps, total_frames):
    total_seconds = total_frames / fps
    hours = int(total_seconds // 3600)
    minutes = int((total_seconds % 3600) // 60)
    seconds = int(total_seconds % 60)
    return f"{hours:02}:{minutes:02}:{seconds:02}"

if __name__ == "__main__":
    fps = float(input("Enter FPS (frames per second): "))
    total_frames = int(input("Enter total number of frames: "))
    timecode = frames_to_timecode(fps, total_frames)
    print("Duration (HH:MM:SS):", timecode)
