import yt_dlp

def download_youtube_video(url, output_path="."):
    try:
        ydl_opts = {
            'outtmpl': f"{output_path}/%(title)s.%(ext)s",  # Save as video title
            'format': 'bestvideo+bestaudio/best',  # Best quality
            # 'merge_output_format': 'mp4',  # Save as MP4
            'merge_output_format': 'mp4',  # Save as MP4
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            ydl.download([url])

        print("Download completed successfully!")

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    video_url = input("Enter YouTube video URL: ")
    download_youtube_video(video_url)