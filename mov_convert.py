import subprocess
import os

# Ask for input filename
input_file = input("Enter video filename (e.g., my_video.mp4): ").strip()

# Extract base name (without extension)
base_name, _ = os.path.splitext(os.path.basename(input_file))

# Define output filename with .mov extension
output_file = f"{base_name}.mov"

# FFmpeg command to convert to ProRes
cmd = [
    "ffmpeg",
    "-i", input_file,
    "-c:v", "prores_ks",
    "-profile:v", "3",         # ProRes 422 HQ
    "-c:a", "pcm_s16le",       # Uncompressed 16-bit audio
    output_file
]

# Run the command
subprocess.run(cmd)

print(f"\n✅ Converted to: {output_file}")
