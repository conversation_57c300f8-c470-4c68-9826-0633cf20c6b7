#!/usr/bin/env python3
"""
Test the specific "இன்" character that was problematic
"""

from indic_transliteration.sanscript import transliterate
from indic_transliteration import sanscript

def test_in_character():
    """Test the specific இன் character"""
    
    test_word = "இன்"
    print(f"Testing: '{test_word}'")
    
    # Test with ISO scheme
    result = transliterate(test_word, sanscript.TAMIL, sanscript.ISO)
    print(f"ISO result: '{result}'")
    
    # Test in context
    test_phrases = [
        "இன்று",      # today
        "இன்னும்",     # still/more
        "இன்பம்",      # happiness
        "இன்னொரு",     # another
    ]
    
    print("\nTesting in context:")
    for phrase in test_phrases:
        result = transliterate(phrase, sanscript.TAMIL, sanscript.ISO)
        print(f"'{phrase}' → '{result}'")

if __name__ == "__main__":
    test_in_character()
