import subprocess
import os

# Ask user for input
input_file = input("Enter video filename (e.g., my_video.mp4): ").strip()
start_time = input("Enter start time (e.g., 00:01:10): ").strip()
end_time = input("Enter end time (e.g., 00:01:40): ").strip()

# Split filename into name and extension
base_name, ext = os.path.splitext(os.path.basename(input_file))

# Clean time strings for filenames
start_clean = start_time.replace(":", "-")
end_clean = end_time.replace(":", "-")

# Create output filename
output_file = f"{base_name}_{start_clean}_{end_clean}{ext}"

# FFmpeg command for fast cut (no re-encoding)
cmd = [
    "ffmpeg",
    "-ss", start_time,
    "-to", end_time,
    "-i", input_file,
    "-c", "copy",
    output_file
]

# Run FFmpeg
subprocess.run(cmd)

print(f"\n✅ Saved: {output_file}")
