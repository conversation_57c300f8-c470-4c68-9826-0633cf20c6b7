import subprocess
import sys
import os

def check_conda():
    """Check if conda is available"""
    try:
        result = subprocess.run(['conda', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Conda found: {result.stdout.strip()}")
            return True
        else:
            return False
    except FileNotFoundError:
        return False

def install_cuda_opencv_conda():
    """Install OpenCV with CUDA using conda"""
    print("🔧 Installing OpenCV with CUDA using conda...")
    
    # Create a new environment or use existing
    env_name = "opencv_cuda"
    print(f"Creating conda environment: {env_name}")
    
    commands = [
        ['conda', 'create', '-n', env_name, 'python=3.11', '-y'],
        ['conda', 'install', '-n', env_name, '-c', 'conda-forge', 'opencv', '-y'],
        ['conda', 'install', '-n', env_name, '-c', 'conda-forge', 'ffmpeg', 'tqdm', '-y']
    ]
    
    for cmd in commands:
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd)
        if result.returncode != 0:
            print(f"❌ Command failed: {' '.join(cmd)}")
            return False
    
    print("✅ Conda installation completed")
    print(f"To use: conda activate {env_name}")
    return True

def install_prebuilt_cuda_opencv():
    """Install pre-built OpenCV with CUDA"""
    print("🔧 Installing pre-built OpenCV with CUDA...")
    
    # Try installing opencv-python with CUDA from a different source
    commands = [
        [sys.executable, '-m', 'pip', 'uninstall', 'opencv-python', 'opencv-contrib-python', '-y'],
        [sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'],
        [sys.executable, '-m', 'pip', 'install', 'opencv-python==********'],
    ]
    
    for cmd in commands:
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd)
        if result.returncode != 0:
            print(f"⚠️ Command had issues: {' '.join(cmd)}")
    
    return True

def create_gpu_optimized_script():
    """Create an optimized version of the automation script for GPU"""
    script_content = '''
import os
import cv2
import numpy as np
import glob
from tqdm import tqdm
import time

def check_gpu_support():
    """Enhanced GPU detection"""
    print("🔍 Checking GPU support...")
    
    # Check OpenCV version
    print(f"OpenCV Version: {cv2.__version__}")
    
    # Check build info for CUDA
    build_info = cv2.getBuildInformation()
    has_cuda_build = "CUDA" in build_info
    print(f"CUDA in build: {has_cuda_build}")
    
    if has_cuda_build:
        try:
            device_count = cv2.cuda.getCudaEnabledDeviceCount()
            print(f"CUDA devices: {device_count}")
            if device_count > 0:
                return True
        except:
            pass
    
    # Fallback: Check for other GPU acceleration
    try:
        # Try to use optimized CPU with threading
        cv2.setNumThreads(cv2.getNumberOfCPUs())
        print(f"Using {cv2.getNumberOfCPUs()} CPU threads for optimization")
        return False
    except:
        return False

def process_video_optimized(video_path, output_path):
    """Optimized video processing"""
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"❌ Cannot open video: {video_path}")
        return False
    
    # Get video properties
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # Setup output
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out_width, out_height = width + 40, height + 40
    out = cv2.VideoWriter(output_path, fourcc, fps, (out_width, out_height))
    
    if not out.isOpened():
        print(f"❌ Cannot create output video: {output_path}")
        return False
    
    # Create overlay
    overlay = np.zeros((100, 200, 4), dtype=np.uint8)
    overlay[:, :, 2] = 255  # Red channel
    overlay[:, :, 3] = 128  # Alpha
    cv2.putText(overlay, "GPU TEST", (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255, 255), 2)
    overlay_resized = cv2.resize(overlay, (out_width, out_height))
    
    print(f"📊 Processing {total_frames} frames ({width}x{height} → {out_width}x{out_height})")
    
    # Process frames
    pbar = tqdm(total=total_frames, desc="Processing", unit="frames")
    start_time = time.time()
    
    frame_count = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # Add padding
        padded = np.zeros((out_height, out_width, 3), dtype=np.uint8)
        padded[20:20+height, 20:20+width] = frame
        
        # Simple overlay (optimized)
        alpha = overlay_resized[:, :, 3:4] / 255.0
        for c in range(3):
            padded[:, :, c] = padded[:, :, c] * (1 - alpha[:, :, 0]) + overlay_resized[:, :, c] * alpha[:, :, 0]
        
        out.write(padded)
        frame_count += 1
        pbar.update(1)
        
        if frame_count % 100 == 0:
            elapsed = time.time() - start_time
            fps_current = frame_count / elapsed if elapsed > 0 else 0
            pbar.set_postfix({"FPS": f"{fps_current:.1f}"})
    
    pbar.close()
    cap.release()
    out.release()
    
    elapsed = time.time() - start_time
    avg_fps = frame_count / elapsed if elapsed > 0 else 0
    print(f"✅ Completed! Processed {frame_count} frames in {elapsed:.1f}s (avg {avg_fps:.1f} FPS)")
    return True

def main():
    # Test video path
    test_path = r"D:\\testing\\marumagal\\original_video"
    
    if not os.path.exists(test_path):
        print(f"❌ Path not found: {test_path}")
        return
    
    # Check GPU support
    gpu_available = check_gpu_support()
    print(f"🚀 GPU acceleration: {'Enabled' if gpu_available else 'CPU Optimized'}")
    
    # Find videos
    videos = glob.glob(os.path.join(test_path, "*.mp4")) + glob.glob(os.path.join(test_path, "*.mov"))
    
    if not videos:
        print(f"❌ No videos found in {test_path}")
        return
    
    # Create output directory
    output_dir = os.path.join(test_path, "gpu_processed")
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"📹 Found {len(videos)} video(s)")
    
    # Process each video
    for video_path in videos:
        filename = os.path.basename(video_path)
        output_path = os.path.join(output_dir, f"processed_{filename}")
        
        print(f"\\n🎬 Processing: {filename}")
        success = process_video_optimized(video_path, output_path)
        
        if success:
            print(f"✅ Saved: {output_path}")
        else:
            print(f"❌ Failed: {filename}")
    
    print(f"\\n🎉 All done! Check: {output_dir}")

if __name__ == "__main__":
    main()
'''
    
    with open("gpu_automation.py", "w") as f:
        f.write(script_content)
    
    print("✅ Created optimized script: gpu_automation.py")

def main():
    print("🚀 CUDA OpenCV Installation Helper\n")
    
    # Check current installation
    try:
        import cv2
        print(f"Current OpenCV: {cv2.__version__}")
        
        # Test CUDA
        try:
            device_count = cv2.cuda.getCudaEnabledDeviceCount()
            if device_count > 0:
                print(f"✅ CUDA working! {device_count} device(s)")
                create_gpu_optimized_script()
                print("\\n🎯 Run: python gpu_automation.py")
                return
            else:
                print("❌ No CUDA devices detected")
        except Exception as e:
            print(f"❌ CUDA not available: {e}")
    except ImportError:
        print("❌ OpenCV not installed")
    
    print("\\n🔧 Installation options:")
    print("1. Try conda installation (recommended)")
    print("2. Use CPU-optimized version")
    print("3. Manual CUDA setup")
    
    choice = input("\\nChoose option (1-3): ").strip()
    
    if choice == "1":
        if check_conda():
            install_cuda_opencv_conda()
        else:
            print("❌ Conda not found. Install Anaconda/Miniconda first.")
            print("Download from: https://www.anaconda.com/")
    elif choice == "2":
        create_gpu_optimized_script()
        print("✅ Created CPU-optimized version: gpu_automation.py")
    else:
        print("\\n📝 Manual CUDA setup:")
        print("1. Install CUDA Toolkit: https://developer.nvidia.com/cuda-downloads")
        print("2. Install cuDNN: https://developer.nvidia.com/cudnn")
        print("3. Build OpenCV from source with CUDA enabled")
        print("4. Or use Docker with CUDA support")

if __name__ == "__main__":
    main()
