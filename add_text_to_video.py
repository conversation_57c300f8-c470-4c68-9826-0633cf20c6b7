import ffmpeg
import os

def main():
    # Get input path and remove any wrapping quotes
    input_path = input("Enter the full video path (with or without quotes): ").strip().strip('"')

    # Validate path
    if not os.path.isfile(input_path):
        print("❌ Invalid file path.")
        return

    # Get overlay text
    overlay_text = input("Enter the text to overlay on the video: ").strip()

    # Probe to get resolution
    try:
        probe = ffmpeg.probe(input_path)
        video_stream = next((s for s in probe['streams'] if s['codec_type'] == 'video'), None)
        if not video_stream:
            print("❌ No video stream found.")
            return
        width = int(video_stream['width'])
        height = int(video_stream['height'])
    except ffmpeg.Error as e:
        print("❌ FFprobe error:", e.stderr.decode())
        return

    font_size = max(24, height // 20)

    # Create output path
    base, ext = os.path.splitext(input_path)
    output_path = f"{base}_with_text{ext}"

    # Run FFmpeg
    try:
        (
            ffmpeg
            .input(input_path)
            .drawtext(
                text=overlay_text,
                fontcolor='white',
                fontsize=font_size,
                box=1,
                boxcolor='black@0.6',
                boxborderw=10,
                x=20,
                y=20
            )
            .output(output_path)
            .overwrite_output()
            .run()
        )
        print(f"✅ Done! Output saved to:\n{output_path}")
    except ffmpeg.Error as e:
        print("❌ FFmpeg error:\n", e.stderr.decode())

if __name__ == "__main__":
    main()
