import os
import subprocess
import ffmpeg


def get_video_info(path):
    """Extract width, height, framerate, duration from a video."""
    result = subprocess.run(
        [
            "ffprobe", "-v", "error", "-select_streams", "v:0",
            "-show_entries", "stream=width,height,r_frame_rate,duration",
            "-of", "default=noprint_wrappers=1:nokey=1", path
        ],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    lines = result.stdout.strip().split('\n')
    width, height = int(lines[0]), int(lines[1])
    fps = eval(lines[2])  # Convert "30/1" or "25/1" to float
    duration = float(lines[3])
    return width, height, fps, duration


def ensure_fps_match(edited_path, target_fps):
    """Force edited video to have the same FPS as the original."""
    tmp_path = "temp_fps_fixed.mp4"
    (
        ffmpeg
        .input(edited_path)
        .filter('fps', fps=target_fps, round='up')
        .output(tmp_path, vcodec='libx264', preset='fast', crf=18)
        .overwrite_output()
        .run(quiet=True)
    )
    return tmp_path


def calculate_fixed_durations(total_duration):
    pre_roll = 3
    side_by_side = 5
    min_post_roll = 5

    total_fixed = pre_roll + side_by_side + min_post_roll

    if total_duration >= total_fixed:
        post_roll = total_duration - pre_roll - side_by_side
    else:
        # Video too short: scale post-roll to fit total duration (minimum zero)
        post_roll = max(0, total_duration - pre_roll - side_by_side)

        if post_roll < 0:
            available = total_duration - pre_roll
            if available <= 0:
                pre_roll = total_duration
                side_by_side = 0
                post_roll = 0
            else:
                total_side_post = 8 + 9
                side_by_side = available * (8 / total_side_post)
                post_roll = available * (9 / total_side_post)

    # Clamp to avoid negatives
    pre_roll = max(pre_roll, 0)
    side_by_side = max(side_by_side, 0)
    post_roll = max(post_roll, 0)

    return pre_roll, side_by_side, post_roll


def main():
    original = input("Path to original video: ").strip().strip('"')
    edited = input("Path to edited video: ").strip().strip('"')
    crop_x = int(input("Crop X (e.g. 100): "))
    start_sec = float(input("Start second (e.g. 0): "))
    out_ext = input("Output extension (e.g. mp4 or mov), leave blank to match original: ").strip()

    orig_width, orig_height, orig_fps, orig_duration = get_video_info(original)
    edit_width, edit_height, edit_fps, edit_duration = get_video_info(edited)

    if not out_ext:
        out_ext = os.path.splitext(original)[-1][1:]  # remove dot

    if abs(orig_fps - edit_fps) > 0.1:
        print("⚠️ FPS mismatch. Converting edited video to match original...")
        edited_fixed = ensure_fps_match(edited, orig_fps)
    else:
        edited_fixed = edited

    total_duration = min(orig_duration, edit_duration) - start_sec
    if total_duration <= 0:
        print("❌ Invalid start second - exceeds video duration.")
        return

    pre_roll_dur, side_by_side_dur, post_roll_dur = calculate_fixed_durations(total_duration)
    print(f"Durations - Pre-roll: {pre_roll_dur:.2f}s, Side-by-side: {side_by_side_dur:.2f}s, Post-roll: {post_roll_dur:.2f}s")

    crop_width = orig_width // 2
    if not (0 <= crop_x <= orig_width - crop_width):
        print(f"❌ Invalid crop_x. It must satisfy: 0 <= x <= {orig_width - crop_width}")
        return

    edited_name = os.path.splitext(os.path.basename(edited))[0]
    edited_dir = os.path.dirname(edited)
    out_dir = os.path.join(edited_dir, "pre_post_reel")
    os.makedirs(out_dir, exist_ok=True)
    out_path = os.path.join(out_dir, f"{edited_name}_demo.{out_ext}")

    size=24

    # Pre-roll original video
    pre_roll = (
    ffmpeg.input(original, ss=start_sec, t=pre_roll_dur).video.drawtext(
        text='ORIGINAL', fontcolor='white', fontsize=orig_height // size,
        box=1, boxcolor='black@0.6', boxborderw=10, x=20, y=20
    ).filter('scale', orig_width, orig_height)
)


    # Side-by-side crop videos
    left_crop = (
    ffmpeg.input(original, ss=start_sec + pre_roll_dur, t=side_by_side_dur).video
    .filter('crop', crop_width, orig_height, crop_x, 0)
    .drawtext(
        text='BEFORE', fontcolor='white', fontsize=orig_height // size,
        box=1, boxcolor='black@0.6', boxborderw=10, x=20, y=20
    ).filter('scale', orig_width // 2, orig_height)
)


    right_crop = (
    ffmpeg.input(edited_fixed, ss=start_sec + pre_roll_dur, t=side_by_side_dur).video
    .filter('crop', crop_width, orig_height, crop_x, 0)
    .drawtext(
        text='AFTER', fontcolor='white', fontsize=orig_height // size,
        box=1, boxcolor='black@0.6', boxborderw=10, x=20, y=20
    ).filter('scale', orig_width // 2, orig_height)
)


    # Post-roll edited video
    post_roll = (
    ffmpeg.input(edited_fixed, ss=start_sec + pre_roll_dur + side_by_side_dur, t=post_roll_dur).video.drawtext(
        text='EDITED', fontcolor='white', fontsize=orig_height // size,
        box=1, boxcolor='black@0.6', boxborderw=10, x=20, y=20
    ).filter('scale', orig_width, orig_height)
)


    # Audio from original for total output duration
    total_out_duration = pre_roll_dur + side_by_side_dur + post_roll_dur
    audio = ffmpeg.input(original, ss=start_sec, t=total_out_duration).audio

    # Stack side-by-side horizontally
    side_by_side_stacked = ffmpeg.filter([left_crop, right_crop], 'hstack')

    # Concatenate pre-roll + side-by-side + post-roll vertically (sequential)
    concat = ffmpeg.concat(pre_roll,
                            side_by_side_stacked,
                            post_roll, v=1, a=0).node

    (
        ffmpeg
        .output(concat[0], audio, out_path, vcodec='libx264', acodec='aac', strict='experimental', shortest=None)
        .overwrite_output()
        .run()
    )

    print(f"✅ Output saved to: {out_path}")


if __name__ == "__main__":
    main()
