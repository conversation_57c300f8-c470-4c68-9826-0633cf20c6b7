import os
import cv2
import numpy as np
import subprocess
import json
import time
import glob
from tqdm import tqdm
from collections import defaultdict
from datetime import timedelta
import ffmpeg

# GPU imports
try:
    import torch
    import torch.nn.functional as F
    GPU_AVAILABLE = torch.cuda.is_available()
    DEVICE = 'cuda' if GPU_AVAILABLE else 'cpu'
except ImportError:
    GPU_AVAILABLE = False
    DEVICE = 'cpu'

def format_time(seconds):
    return str(timedelta(seconds=int(seconds))).zfill(8)

def calculate_histogram_diff(frame1, frame2):
    hsv1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2HSV)
    hsv2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2HSV)
    hist1 = cv2.calcHist([hsv1], [0, 1], None, [50, 60], [0, 180, 0, 256])
    hist2 = cv2.calcHist([hsv2], [0, 1], None, [50, 60], [0, 180, 0, 256])
    cv2.normalize(hist1, hist1)
    cv2.normalize(hist2, hist2)
    return 1 - cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)

def extract_clip(input_video, start_time, duration, output_path):
    try:
        ffmpeg.input(input_video, ss=start_time).output(
            output_path, t=duration, map='0:v', c='copy', y=None
        ).overwrite_output().run(quiet=True)
    except ffmpeg.Error as e:
        print(f"❌ FFmpeg error: {e}")

def save_first_frame(video_path, frame_number, output_path, clip_number, start_frame, end_frame, duration):
    cap = cv2.VideoCapture(video_path)
    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
    ret, frame = cap.read()
    if ret:
        width, height = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)), int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        text_lines = [
            f"Clip {clip_number}", f"Start: {start_frame}", f"End: {end_frame}",
            f"Duration: {duration:.2f}s", f"Resolution: {width}x{height}"
        ]
        for i, line in enumerate(text_lines):
            cv2.putText(frame, line, (10, 40 + i * 40), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 255, 0), 3)
        cv2.imwrite(output_path, frame)
    cap.release()

def cluster_clips_by_histogram(all_metadata, first_frames_dir, similarity=0.15):
    groups, histograms = [], []
    for clip in tqdm(all_metadata, desc="📊 Grouping clips"):
        img_path = os.path.join(first_frames_dir, clip["first_frame_image"])
        img = cv2.imread(img_path)
        if img is None:
            continue
        
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        hist = cv2.calcHist([hsv], [0, 1], None, [50, 60], [0, 180, 0, 256])
        cv2.normalize(hist, hist)
        
        placed = False
        for i, group_hist in enumerate(histograms):
            if 1 - cv2.compareHist(hist, group_hist, cv2.HISTCMP_CORREL) < similarity:
                groups[i].append(clip)
                histograms[i] = (group_hist * (len(groups[i]) - 1) + hist) / len(groups[i])
                placed = True
                break
        
        if not placed:
            groups.append([clip])
            histograms.append(hist)
    
    return groups

def process_video(video_path, clips_dir, first_frames_dir, clip_start_index, video_index, threshold=0.15, min_scene_gap=2):
    cap = cv2.VideoCapture(video_path)
    fps = cap.get(cv2.CAP_PROP_FPS)
    width, height = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)), int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    prev_frame, frame_id, last_scene_frame, clip_index = None, 0, 0, clip_start_index
    metadata = []
    video_id = f"vid{video_index + 1:04d}"
    
    pbar = tqdm(total=total_frames, desc=f"Processing {os.path.basename(video_path)}")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        if prev_frame is not None:
            diff = calculate_histogram_diff(prev_frame, frame)
            if diff > threshold and (frame_id - last_scene_frame) > min_scene_gap:
                start_time = last_scene_frame / fps
                duration = (frame_id - last_scene_frame) / fps
                ext = os.path.splitext(video_path)[-1]
                clip_name = f"clip_{clip_index:04d}_{last_scene_frame:06d}_{frame_id:06d}_{duration:.2f}s_{start_time:.2f}s_{width}x{height}_{video_id}{ext}"
                
                extract_clip(video_path, start_time, duration, os.path.join(clips_dir, clip_name))
                save_first_frame(video_path, last_scene_frame, 
                               os.path.join(first_frames_dir, clip_name.replace(ext, ".jpg")),
                               clip_index, last_scene_frame, frame_id, duration)
                
                metadata.append({
                    "clip": clip_name, "clip_no": clip_index, "video_id": video_id,
                    "source_video": os.path.basename(video_path), "start_frame": last_scene_frame,
                    "end_frame": frame_id, "start_time": round(start_time, 3),
                    "duration": round(duration, 3), "first_frame_image": clip_name.replace(ext, ".jpg")
                })
                
                clip_index += 1
                last_scene_frame = frame_id
        
        prev_frame = frame
        frame_id += 1
        pbar.update(1)
    
    # Handle final clip
    if frame_id > last_scene_frame:
        start_time = last_scene_frame / fps
        duration = (frame_id - last_scene_frame) / fps
        ext = os.path.splitext(video_path)[-1]
        clip_name = f"clip_{clip_index:04d}_{last_scene_frame:06d}_{frame_id:06d}_{duration:.2f}s_{start_time:.2f}s_{width}x{height}_{video_id}{ext}"
        
        extract_clip(video_path, start_time, duration, os.path.join(clips_dir, clip_name))
        save_first_frame(video_path, last_scene_frame,
                       os.path.join(first_frames_dir, clip_name.replace(ext, ".jpg")),
                       clip_index, last_scene_frame, frame_id, duration)
        
        metadata.append({
            "clip": clip_name, "clip_no": clip_index, "video_id": video_id,
            "source_video": os.path.basename(video_path), "start_frame": last_scene_frame,
            "end_frame": frame_id, "start_time": round(start_time, 3),
            "duration": round(duration, 3), "first_frame_image": clip_name.replace(ext, ".jpg")
        })
    
    pbar.close()
    cap.release()
    return metadata, clip_index

def gpu_process_frame(frame, overlay, pad=20):
    """GPU-accelerated frame processing"""
    if not GPU_AVAILABLE:
        return cpu_process_frame(frame, overlay, pad)
    
    try:
        h, w = frame.shape[:2]
        
        # Convert to tensor and add alpha
        frame_tensor = torch.from_numpy(frame).float().to(DEVICE)
        alpha = torch.full((h, w, 1), 255.0, device=DEVICE)
        frame_bgra = torch.cat([frame_tensor, alpha], dim=2)
        
        # Add padding
        padded = F.pad(frame_bgra.permute(2, 0, 1), (pad, pad, pad, pad)).permute(1, 2, 0)
        
        # Resize overlay and blend
        overlay_tensor = torch.from_numpy(overlay).float().to(DEVICE)
        if overlay_tensor.shape[:2] != padded.shape[:2]:
            overlay_tensor = F.interpolate(
                overlay_tensor.permute(2, 0, 1).unsqueeze(0),
                size=padded.shape[:2], mode='bilinear'
            ).squeeze(0).permute(1, 2, 0)
        
        # Alpha blending
        alpha_overlay = overlay_tensor[:, :, 3:4] / 255.0
        result = padded.clone()
        result[:, :, :3] = (1 - alpha_overlay) * padded[:, :, :3] + alpha_overlay * overlay_tensor[:, :, :3]
        
        return result[:, :, :3].cpu().numpy().astype(np.uint8)
    except Exception:
        return cpu_process_frame(frame, overlay, pad)

def cpu_process_frame(frame, overlay, pad=20):
    """CPU fallback processing"""
    h, w = frame.shape[:2]
    padded = np.zeros((h + pad * 2, w + pad * 2, 3), dtype=np.uint8)
    padded[pad:pad + h, pad:pad + w] = frame
    
    # Simple overlay
    overlay_resized = cv2.resize(overlay, (padded.shape[1], padded.shape[0]))
    if overlay_resized.shape[2] == 4:
        alpha = overlay_resized[:, :, 3:4] / 255.0
        for c in range(3):
            padded[:, :, c] = padded[:, :, c] * (1 - alpha[:, :, 0]) + overlay_resized[:, :, c] * alpha[:, :, 0]
    
    return padded

def process_video_overlay(video_path, overlay_img, output_path):
    """Optimized video overlay processing"""
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"❌ Cannot open: {video_path}")
        return
    
    fps = cap.get(cv2.CAP_PROP_FPS)
    w, h = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)), int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (w + 40, h + 40))
    
    print(f"📊 Processing {total_frames} frames with {'GPU' if GPU_AVAILABLE else 'CPU'}")
    
    pbar = tqdm(total=total_frames, desc=os.path.basename(video_path), unit="fps")
    start_time = time.time()
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        processed = gpu_process_frame(frame, overlay_img) if GPU_AVAILABLE else cpu_process_frame(frame, overlay_img)
        out.write(processed)
        pbar.update(1)
    
    elapsed = time.time() - start_time
    fps_actual = total_frames / elapsed if elapsed > 0 else 0
    
    pbar.close()
    cap.release()
    out.release()
    
    print(f"✅ Completed in {elapsed:.1f}s ({fps_actual:.1f} FPS)")

def main():
    print("🚀 Video Processing Tool")
    if GPU_AVAILABLE:
        print(f"🔥 GPU: {torch.cuda.get_device_name(0)}")
    else:
        print("💻 Using CPU processing")
    
    while True:
        original_shots_path = input("\n📁 Enter path to 'original_shots': ").strip()
        if os.path.isdir(original_shots_path):
            parent_dir = os.path.abspath(os.path.join(original_shots_path, os.pardir))
            break
        print("❌ Invalid path")
    
    while True:
        print("\nChoose task:")
        print("1: Extract clips  2: Organize clips  3: Format correction")
        print("4: Merge by product  5: Final merge  6: Merge by folder")
        print("7: Cut original  8: Overlay & padding  exit: Exit")
        
        choice = input("Choice: ").strip()
        
        if choice == '8':
            test_path = r"D:\testing\marumagal\original_video"
            if not os.path.exists(test_path):
                print(f"❌ Path not found: {test_path}")
                continue
            
            output_dir = os.path.join(test_path, "overlayed_logo")
            os.makedirs(output_dir, exist_ok=True)
            
            # Create test overlay
            overlay = np.zeros((100, 200, 4), dtype=np.uint8)
            overlay[:, :, 0] = 255  # Blue
            overlay[:, :, 3] = 128  # Alpha
            cv2.putText(overlay, "GPU LOGO", (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255, 255), 2)
            
            videos = glob.glob(os.path.join(test_path, "*.mp4")) + glob.glob(os.path.join(test_path, "*.mov"))
            
            for video_path in videos:
                filename = os.path.basename(video_path)
                output_path = os.path.join(output_dir, filename)
                print(f"\n🎬 Processing: {filename}")
                process_video_overlay(video_path, overlay, output_path)
            
            print(f"🎉 Done! Output: {output_dir}")
        
        elif choice.lower() == 'exit':
            break
        else:
            print("❌ Invalid choice")

if __name__ == "__main__":
    main()
