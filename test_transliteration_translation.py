#!/usr/bin/env python3
"""
Test the new transliteration + translation functionality
"""

import re
from indic_transliteration.sanscript import transliterate
from indic_transliteration import sanscript
from googletrans import Translator

# Tamil unicode range
tamil_chars_re = re.compile(r'[\u0B80-\u0BFF]')

def has_tamil_characters(text):
    """Check if text contains any Tamil characters"""
    return bool(tamil_chars_re.search(text))

def test_transliteration_with_translation():
    """Test the new functionality with sample Tamil text"""
    
    translator = Translator()
    
    # Sample Tamil sentences
    test_sentences = [
        "அப்போது எனக்கு 27 வயதுதான்",  # Then for me 27 just age
        "நான் நன்றாக இருக்கிறேன்",      # I am fine
        "இது ஒரு சோதனை",              # This is a test
    ]
    
    print("🧪 Testing Transliteration + Translation:")
    print("=" * 70)
    
    for i, tamil_text in enumerate(test_sentences, 1):
        print(f"\nTest {i}:")
        print(f"Tamil: {tamil_text}")
        print("-" * 50)
        
        try:
            # Get transliteration
            transliterated = transliterate(tamil_text, sanscript.TAMIL, sanscript.ISO)
            print(f"Transliteration: {transliterated}")
            
            # Get word-by-word translation
            words = re.findall(r'\S+', tamil_text)
            transliterated_words = []
            translated_words = []
            
            for word in words:
                # Remove punctuation for translation
                clean_word = re.sub(r'[^\u0B80-\u0BFF]+', '', word)
                
                if clean_word and has_tamil_characters(clean_word):
                    # Transliterate the word
                    try:
                        trans_word = transliterate(clean_word, sanscript.TAMIL, sanscript.ISO)
                        transliterated_words.append(trans_word)
                    except:
                        transliterated_words.append(clean_word)
                    
                    # Translate the word
                    try:
                        translated = translator.translate(clean_word, src='ta', dest='en').text
                        translated_words.append(f"'{translated}'")
                    except Exception as e:
                        print(f"Translation error for '{clean_word}': {e}")
                        translated_words.append(f"'{clean_word}'")
            
            # Display the result in the requested format
            if transliterated_words and translated_words:
                print("Result format:")
                print(' '.join(transliterated_words))
                print(' '.join(translated_words))
            
            print("✅ Success")
            
        except Exception as e:
            print(f"❌ ERROR: {e}")
    
    print("\n" + "=" * 70)
    print("Test completed!")

if __name__ == "__main__":
    test_transliteration_with_translation()
