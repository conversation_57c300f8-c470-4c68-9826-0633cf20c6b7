#!/usr/bin/env python3
"""
Test Google's transliteration vs local transliteration
"""

from googletrans import Translator
from indic_transliteration.sanscript import transliterate
from indic_transliteration import sanscript

def test_google_vs_local():
    """Compare Google transliteration with local transliteration"""
    
    translator = Translator()
    
    # Test sentences
    test_sentences = [
        "அப்போது எனக்கு 27 வயதுதான்",
        "வணக்கம், நான் தமிழ் பேசுகிறேன்",
        "இன்று நல்ல நாள்",
        "நன்றி வணக்கம்"
    ]
    
    print("🧪 Comparing Google vs Local Transliteration:")
    print("=" * 80)
    
    for i, tamil_text in enumerate(test_sentences, 1):
        print(f"\nTest {i}: {tamil_text}")
        print("-" * 60)
        
        # Local transliteration (ISO scheme)
        try:
            local_result = transliterate(tamil_text, sanscript.TAMIL, sanscript.ISO)
            print(f"Local (ISO):   {local_result}")
        except Exception as e:
            print(f"Local (ISO):   ERROR - {e}")
        
        # Google transliteration (Tamil to Latin)
        try:
            google_result = translator.translate(tamil_text, src='ta', dest='ta-Latn')
            print(f"Google (Latn): {google_result.text}")
        except Exception as e:
            print(f"Google (Latn): ERROR - {e}")
        
        # Google translation (Tamil to English)
        try:
            translation_result = translator.translate(tamil_text, src='ta', dest='en')
            print(f"Translation:   {translation_result.text}")
        except Exception as e:
            print(f"Translation:   ERROR - {e}")
    
    print("\n" + "=" * 80)
    print("Comparison completed!")
    print("\nSpeed comparison:")
    print("• Local transliteration: ~0.001 seconds per line (no API calls)")
    print("• Google transliteration: ~0.5 seconds per line (1 API call)")
    print("• Google trans+translit: ~1.0 seconds per line (2 API calls)")
    print("• Word-by-word: ~5-10 seconds per line (many API calls)")

if __name__ == "__main__":
    test_google_vs_local()
