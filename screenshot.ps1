param (
    [int]$Delay = 10
)

# Wait for the specified delay
Start-Sleep -Seconds $Delay

# Load necessary assemblies
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# Capture the primary screen
$bounds = [System.Windows.Forms.Screen]::PrimaryScreen.Bounds
$bitmap = New-Object System.Drawing.Bitmap $bounds.Width, $bounds.Height
$graphics = [System.Drawing.Graphics]::FromImage($bitmap)
$graphics.CopyFromScreen($bounds.Location, [System.Drawing.Point]::Empty, $bounds.Size)

# Save the screenshot with a timestamp
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$output = "$env:USERPROFILE\screenshot_$timestamp.png"
$bitmap.Save($output, [System.Drawing.Imaging.ImageFormat]::Png)

# Dispose objects
$graphics.Dispose()
$bitmap.Dispose()

# Copy path to clipboard
Set-Clipboard -Value $output

# Open the screenshot
Start-Process $output

# Output confirmation message
Write-Output "Screenshot saved to: $output (also copied to clipboard)"
