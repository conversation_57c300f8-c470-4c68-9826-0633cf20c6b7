#!/usr/bin/env python3
"""
Test specific Tamil characters that were problematic
"""

import re
from indic_transliteration.sanscript import transliterate
from indic_transliteration import sanscript
import unicodedata

# Tamil unicode range
tamil_chars_re = re.compile(r'[\u0B80-\u0BFF]')

def test_specific_chars():
    """Test specific problematic Tamil characters"""
    
    # Test cases with expected results
    test_cases = [
        ("இன்", "Should be 'iṉ' or similar"),
        ("இ", "Should be 'i'"),
        ("ன்", "Should be 'ṉ' or 'n'"),
        ("வணக்கம்", "Should be 'vaṇakkam' or similar"),
        ("நன்றி", "Should be 'naṉṟi' or similar"),
    ]
    
    schemes = [
        ("ISO", sanscript.ISO),
        ("IAST", sanscript.IAST), 
        ("ITRANS", sanscript.ITRANS),
        ("HK", sanscript.HK)
    ]
    
    print("🧪 Testing specific Tamil characters:")
    print("=" * 80)
    
    for tamil_text, expected in test_cases:
        print(f"\nTesting: '{tamil_text}' ({expected})")
        print("-" * 50)
        
        # Show Unicode details
        for char in tamil_text:
            if tamil_chars_re.match(char):
                try:
                    name = unicodedata.name(char)
                    print(f"  '{char}' = {name} (U+{ord(char):04X})")
                except:
                    print(f"  '{char}' = Unknown (U+{ord(char):04X})")
        
        print("\nTransliteration results:")
        for scheme_name, scheme in schemes:
            try:
                result = transliterate(tamil_text, sanscript.TAMIL, scheme)
                has_tamil = bool(tamil_chars_re.search(result))
                status = "❌ HAS TAMIL" if has_tamil else "✅ CLEAN"
                print(f"  {scheme_name:6}: '{result}' {status}")
            except Exception as e:
                print(f"  {scheme_name:6}: ERROR - {e}")
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    test_specific_chars()
