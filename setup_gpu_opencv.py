import subprocess
import sys
import os

def check_nvidia_gpu():
    """Check if NVIDIA GPU is available"""
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ NVIDIA GPU detected:")
            lines = result.stdout.split('\n')
            for line in lines:
                if 'GeForce' in line or 'RTX' in line or 'GTX' in line or 'Quadro' in line:
                    print(f"   {line.strip()}")
            return True
        else:
            print("❌ nvidia-smi command failed")
            return False
    except FileNotFoundError:
        print("❌ nvidia-smi not found. NVIDIA drivers may not be installed.")
        return False

def install_opencv_cuda():
    """Install OpenCV with CUDA support"""
    print("\n🔧 Installing OpenCV with CUDA support...")
    
    # First, uninstall existing OpenCV
    print("Removing existing OpenCV installations...")
    subprocess.run([sys.executable, '-m', 'pip', 'uninstall', 'opencv-python', 'opencv-contrib-python', '-y'])
    
    # Install OpenCV with CUDA support
    print("Installing opencv-contrib-python (includes CUDA support)...")
    result = subprocess.run([
        sys.executable, '-m', 'pip', 'install', 
        'opencv-contrib-python==********'  # This version often has better CUDA support
    ])
    
    if result.returncode == 0:
        print("✅ OpenCV installation completed")
        return True
    else:
        print("❌ OpenCV installation failed")
        return False

def test_cuda_opencv():
    """Test if OpenCV can access CUDA"""
    try:
        import cv2
        print(f"\n🔍 Testing OpenCV {cv2.__version__}")
        
        # Check build info
        build_info = cv2.getBuildInformation()
        if "CUDA" in build_info:
            print("✅ OpenCV compiled with CUDA support")
            
            # Check for CUDA devices
            try:
                device_count = cv2.cuda.getCudaEnabledDeviceCount()
                if device_count > 0:
                    print(f"✅ {device_count} CUDA device(s) detected by OpenCV")
                    
                    # Test basic GPU operations
                    import numpy as np
                    test_img = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
                    gpu_img = cv2.cuda_GpuMat()
                    gpu_img.upload(test_img)
                    gpu_gray = cv2.cuda.cvtColor(gpu_img, cv2.COLOR_BGR2GRAY)
                    result = gpu_gray.download()
                    print("✅ GPU operations working correctly")
                    return True
                else:
                    print("❌ No CUDA devices found")
                    return False
            except Exception as e:
                print(f"❌ CUDA operations failed: {e}")
                return False
        else:
            print("❌ OpenCV not compiled with CUDA support")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import OpenCV: {e}")
        return False

def main():
    print("🚀 GPU OpenCV Setup Tool\n")
    
    # Check for NVIDIA GPU
    if not check_nvidia_gpu():
        print("\n❌ No NVIDIA GPU detected. GPU acceleration requires NVIDIA GPU.")
        return
    
    # Test current OpenCV
    print("\n🔍 Testing current OpenCV installation...")
    if test_cuda_opencv():
        print("\n🎉 Your OpenCV already has working CUDA support!")
        return
    
    # Install OpenCV with CUDA
    print("\n⚠️ Current OpenCV doesn't have CUDA support.")
    response = input("Do you want to install OpenCV with CUDA support? (y/n): ")
    
    if response.lower() == 'y':
        if install_opencv_cuda():
            print("\n🔍 Testing new installation...")
            if test_cuda_opencv():
                print("\n🎉 Success! OpenCV with CUDA is now working!")
                print("\nYou can now run your automation script with GPU acceleration.")
            else:
                print("\n❌ Installation completed but CUDA still not working.")
                print("You may need to:")
                print("1. Install CUDA Toolkit from NVIDIA")
                print("2. Install cuDNN")
                print("3. Build OpenCV from source with CUDA enabled")
        else:
            print("\n❌ Installation failed.")
    else:
        print("\nSkipping installation. Your script will use CPU processing.")
    
    print("\n📝 Alternative solutions:")
    print("1. Use pre-built OpenCV with CUDA from conda-forge:")
    print("   conda install -c conda-forge opencv")
    print("2. Build OpenCV from source with CUDA enabled")
    print("3. Use Docker with pre-built OpenCV+CUDA image")

if __name__ == "__main__":
    main()
