#!/usr/bin/env python3
"""
Test the progress bars and file naming functionality
"""

import os
import tempfile
from tamil_to_english_transcript import generate_output_path

def test_file_naming():
    """Test the automatic file naming with incremental numbers"""
    
    print("🧪 Testing automatic file naming:")
    print("=" * 50)
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create a test input file path
        input_file = os.path.join(temp_dir, "test_subtitle.srt")
        
        # Test 1: No existing file
        output1 = generate_output_path(input_file)
        print(f"Test 1 - No existing file:")
        print(f"  Input:  {os.path.basename(input_file)}")
        print(f"  Output: {os.path.basename(output1)}")
        
        # Create the first output file
        with open(output1, 'w') as f:
            f.write("test")
        
        # Test 2: One existing file
        output2 = generate_output_path(input_file)
        print(f"\nTest 2 - One existing file:")
        print(f"  Input:  {os.path.basename(input_file)}")
        print(f"  Output: {os.path.basename(output2)}")
        
        # Create the second output file
        with open(output2, 'w') as f:
            f.write("test")
        
        # Test 3: Two existing files
        output3 = generate_output_path(input_file)
        print(f"\nTest 3 - Two existing files:")
        print(f"  Input:  {os.path.basename(input_file)}")
        print(f"  Output: {os.path.basename(output3)}")
        
        # Test 4: Different extension
        input_file_txt = os.path.join(temp_dir, "test_subtitle.txt")
        output4 = generate_output_path(input_file_txt)
        print(f"\nTest 4 - Different extension:")
        print(f"  Input:  {os.path.basename(input_file_txt)}")
        print(f"  Output: {os.path.basename(output4)}")
    
    print("\n✅ File naming test completed!")

def create_sample_srt():
    """Create a sample SRT file for testing"""
    
    sample_content = """1
00:00:01,000 --> 00:00:03,000
வணக்கம், நான் தமிழ் பேசுகிறேன்

2
00:00:04,000 --> 00:00:06,000
இது ஒரு சோதனை

3
00:00:07,000 --> 00:00:09,000
நன்றி

"""
    
    with open("sample_tamil.srt", "w", encoding="utf-8") as f:
        f.write(sample_content)
    
    print("📝 Created sample_tamil.srt for testing")
    return "sample_tamil.srt"

if __name__ == "__main__":
    test_file_naming()
    
    print("\n" + "=" * 50)
    print("Creating sample file for manual testing...")
    sample_file = create_sample_srt()
    print(f"✅ Sample file created: {sample_file}")
    print("\nYou can now run:")
    print("python tamil_to_english_transcript.py")
    print("And use the sample file to test the progress bars!")
