import os
import subprocess

def convert_mov_to_mp4(folder_path):
    if not os.path.isdir(folder_path):
        print("❌ Invalid folder path.")
        return

    for filename in os.listdir(folder_path):
        if filename.lower().endswith(".mov"):
            mov_path = os.path.join(folder_path, filename)
            mp4_filename = os.path.splitext(filename)[0] + ".mp4"
            mp4_path = os.path.join(folder_path, mp4_filename)

            print(f"🔄 Converting: {filename} → {mp4_filename}")
            command = [
                "ffmpeg", "-y", "-i", mov_path,
                "-vcodec", "libx264", "-acodec", "aac",
                mp4_path
            ]
            try:
                subprocess.run(command, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                print(f"✅ Saved: {mp4_path}")
            except Exception as e:
                print(f"❌ Failed to convert {filename}: {e}")

if __name__ == "__main__":
    folder = input("📁 Enter the folder path containing .mov files: ").strip()
    convert_mov_to_mp4(folder)
