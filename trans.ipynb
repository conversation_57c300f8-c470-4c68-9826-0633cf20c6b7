{"cells": [{"cell_type": "code", "execution_count": null, "id": "a3fc2e06", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "def hex_dump(file_path, num_bytes=1024, bytes_per_line=16):\n", "    file_path = Path(file_path.strip('\"'))  # support quoted Windows paths\n", "\n", "    try:\n", "        with open(file_path, \"rb\") as f:\n", "            data = f.read(num_bytes)\n", "        \n", "        print(f\"🔍 Hex Dump of: {file_path}\\n\")\n", "        print(f\"{'Offset':<8}  {'Hex Bytes':<{bytes_per_line*3}}  ASCII\")\n", "        print(\"-\" * (8 + 2 + bytes_per_line*3 + 2 + bytes_per_line))\n", "\n", "        for i in range(0, len(data), bytes_per_line):\n", "            chunk = data[i:i+bytes_per_line]\n", "            hex_part = ' '.join(f\"{b:02X}\" for b in chunk)\n", "            ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)\n", "            print(f\"{i:08X}  {hex_part:<{bytes_per_line*3}}  {ascii_part}\")\n", "\n", "    except FileNotFoundError:\n", "        print(f\"❌ File not found: {file_path}\")\n", "    except Exception as e:\n", "        print(f\"⚠️ Error: {e}\")\n"]}, {"cell_type": "code", "execution_count": 6, "id": "99ab24ed", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Hex Dump of: D:\\harsh\\harsh\\AppData\\Local\\Packages\\Microsoft.WindowsNotepad_8wekyb3d8bbwe\\LocalState\\TabState\\3b0cc573-4701-43f1-97cb-1c33c990d8c0.0.bin\n", "\n", "Offset    Hex Bytes                                         ASCII\n", "----------------------------------------------------------------------------\n", "00000000  4E 50 00 0B 00 D0 02 00 00 01 00 00 02 01 01 A9   NP..............\n", "00000010  C5 43 5F                                          .C_\n"]}], "source": ["hex_dump(r\"D:\\harsh\\harsh\\AppData\\Local\\Packages\\Microsoft.WindowsNotepad_8wekyb3d8bbwe\\LocalState\\TabState\\3b0cc573-4701-43f1-97cb-1c33c990d8c0.0.bin\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 5}