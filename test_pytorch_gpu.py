import torch
import cv2
import numpy as np
import time

def test_pytorch_gpu():
    print("🚀 Testing PyTorch GPU Acceleration")
    print("=" * 50)
    
    # Check GPU availability
    if torch.cuda.is_available():
        device = torch.cuda.get_device_name(0)
        print(f"✅ GPU Available: {device}")
        print(f"   CUDA Version: {torch.version.cuda}")
        print(f"   Device Count: {torch.cuda.device_count()}")
    else:
        print("❌ GPU not available")
        return
    
    # Test basic GPU operations
    print("\n🧪 Testing GPU operations...")
    
    # Create test data
    test_size = (1080, 1920, 3)  # HD video frame size
    test_frame = np.random.randint(0, 255, test_size, dtype=np.uint8)
    
    print(f"Test frame size: {test_frame.shape}")
    
    # CPU processing time
    print("\n⏱️ CPU Processing:")
    start_time = time.time()
    for i in range(10):
        # Simulate padding
        padded = np.zeros((test_size[0] + 40, test_size[1] + 40, 4), dtype=np.uint8)
        padded[20:20+test_size[0], 20:20+test_size[1], :3] = test_frame
        padded[:, :, 3] = 255
    cpu_time = time.time() - start_time
    print(f"   10 iterations: {cpu_time:.3f}s ({cpu_time/10*1000:.1f}ms per frame)")
    
    # GPU processing time
    print("\n🚀 GPU Processing:")
    device = 'cuda'
    start_time = time.time()
    
    for i in range(10):
        # Convert to tensor and move to GPU
        frame_tensor = torch.from_numpy(test_frame).float().to(device)
        
        # Add alpha channel
        alpha = torch.full((test_size[0], test_size[1], 1), 255.0, device=device)
        frame_with_alpha = torch.cat([frame_tensor, alpha], dim=2)
        
        # Add padding
        frame_padded = torch.nn.functional.pad(
            frame_with_alpha.permute(2, 0, 1), 
            (20, 20, 20, 20), 
            mode='constant', 
            value=0
        ).permute(1, 2, 0)
        
        # Move back to CPU
        result = frame_padded.cpu().numpy().astype(np.uint8)
    
    gpu_time = time.time() - start_time
    print(f"   10 iterations: {gpu_time:.3f}s ({gpu_time/10*1000:.1f}ms per frame)")
    
    # Calculate speedup
    speedup = cpu_time / gpu_time
    print(f"\n📊 Performance Results:")
    print(f"   CPU: {cpu_time/10*1000:.1f}ms per frame")
    print(f"   GPU: {gpu_time/10*1000:.1f}ms per frame")
    print(f"   Speedup: {speedup:.1f}x faster with GPU!")
    
    # Test memory usage
    print(f"\n💾 GPU Memory:")
    if torch.cuda.is_available():
        memory_allocated = torch.cuda.memory_allocated() / 1024**2  # MB
        memory_reserved = torch.cuda.memory_reserved() / 1024**2   # MB
        print(f"   Allocated: {memory_allocated:.1f} MB")
        print(f"   Reserved: {memory_reserved:.1f} MB")
    
    print("\n🎉 PyTorch GPU acceleration is working!")
    return True

def test_video_processing_speed():
    """Test actual video processing speed"""
    print("\n🎬 Testing Video Processing Speed")
    print("=" * 50)
    
    # Simulate video processing
    frame_count = 100
    frame_size = (1080, 1920, 3)
    
    print(f"Simulating {frame_count} frames of {frame_size[1]}x{frame_size[0]} video")
    
    # Generate test frames
    frames = []
    for i in range(frame_count):
        frame = np.random.randint(0, 255, frame_size, dtype=np.uint8)
        frames.append(frame)
    
    # Test GPU processing
    if torch.cuda.is_available():
        print("\n🚀 GPU Processing:")
        start_time = time.time()
        
        device = 'cuda'
        for i, frame in enumerate(frames):
            # Convert to tensor
            frame_tensor = torch.from_numpy(frame).float().to(device)
            
            # Add alpha and padding (simplified)
            alpha = torch.full((frame.shape[0], frame.shape[1], 1), 255.0, device=device)
            frame_with_alpha = torch.cat([frame_tensor, alpha], dim=2)
            
            # Simple processing
            processed = frame_with_alpha * 0.8  # Simulate some processing
            result = processed.cpu().numpy().astype(np.uint8)
            
            if i % 20 == 0:
                print(f"   Processed frame {i+1}/{frame_count}")
        
        gpu_total_time = time.time() - start_time
        gpu_fps = frame_count / gpu_total_time
        
        print(f"   Total time: {gpu_total_time:.2f}s")
        print(f"   Processing speed: {gpu_fps:.1f} FPS")
        
        return gpu_fps
    
    return 0

if __name__ == "__main__":
    if test_pytorch_gpu():
        fps = test_video_processing_speed()
        print(f"\n🎯 Expected video processing speed: ~{fps:.0f} FPS")
        print("Your automation script should be much faster now!")
    else:
        print("❌ GPU testing failed")
