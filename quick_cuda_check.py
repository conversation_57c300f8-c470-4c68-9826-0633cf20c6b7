import subprocess
import sys
import os

def check_cuda_toolkit():
    """Check if CUDA toolkit is already installed"""
    print("🔍 Checking existing CUDA installation...")
    
    # Check nvcc
    try:
        result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ CUDA Toolkit already installed!")
            print(result.stdout)
            return True
        else:
            print("❌ nvcc not found")
            return False
    except FileNotFoundError:
        print("❌ CUDA Toolkit not found")
        return False

def install_opencv_cuda_fast():
    """Fast OpenCV CUDA installation"""
    print("\n📷 Installing OpenCV with CUDA support...")
    
    commands = [
        [sys.executable, "-m", "pip", "uninstall", "opencv-python", "opencv-contrib-python", "-y"],
        [sys.executable, "-m", "pip", "install", "numpy<2"],
        [sys.executable, "-m", "pip", "install", "opencv-contrib-python==********"],
    ]
    
    for cmd in commands:
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd)
        if result.returncode == 0:
            print("✅ Command successful")
        else:
            print("⚠️ Command had issues, continuing...")

def test_opencv_cuda():
    """Test OpenCV CUDA"""
    print("\n🧪 Testing OpenCV CUDA...")
    
    test_code = '''
import cv2
import numpy as np

print(f"OpenCV version: {cv2.__version__}")

# Check for CUDA in build
build_info = cv2.getBuildInformation()
has_cuda_build = "CUDA" in build_info
print(f"CUDA in build info: {has_cuda_build}")

# Try CUDA operations
try:
    device_count = cv2.cuda.getCudaEnabledDeviceCount()
    print(f"CUDA devices detected: {device_count}")
    
    if device_count > 0:
        # Test basic operation
        test_img = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        gpu_img = cv2.cuda_GpuMat()
        gpu_img.upload(test_img)
        gpu_gray = cv2.cuda.cvtColor(gpu_img, cv2.COLOR_BGR2GRAY)
        result = gpu_gray.download()
        print("✅ GPU operations working!")
        print("🚀 CUDA acceleration is READY!")
    else:
        print("❌ No CUDA devices found")
        
except Exception as e:
    print(f"❌ CUDA operations failed: {e}")
    print("OpenCV doesn't have CUDA support")
'''
    
    try:
        result = subprocess.run([sys.executable, "-c", test_code], 
                              capture_output=True, text=True)
        print("Test Results:")
        print(result.stdout)
        if result.stderr:
            print("Errors:")
            print(result.stderr)
        
        return "GPU operations working!" in result.stdout
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def update_automation_script():
    """Update the automation script to show GPU status"""
    print("\n🔧 Creating GPU test for automation script...")
    
    test_script = '''
import sys
import os
sys.path.append(r"C:\\Users\\<USER>\\scripts")

def test_automation_gpu():
    print("🚀 Testing Automation Script GPU Support")
    print("=" * 50)
    
    try:
        # Import the automation functions
        from automation import is_cuda_available
        
        # Test GPU availability
        gpu_available = is_cuda_available()
        
        if gpu_available:
            print("🎉 SUCCESS! GPU acceleration is working!")
            print("Your automation script will now use GPU acceleration.")
            print("Expected performance boost: 3-5x faster processing")
        else:
            print("⚠️ GPU not available, using optimized CPU")
            print("Still much faster than before due to optimizations")
        
        # Test video path
        test_path = r"D:\\testing\\marumagal\\original_video"
        if os.path.exists(test_path):
            print(f"✅ Test video path ready: {test_path}")
        else:
            print(f"❌ Test video path not found: {test_path}")
        
        print("\\n🎬 Ready to run automation.py with choice 8!")
        print("Run: python automation.py")
        
    except ImportError as e:
        print(f"❌ Could not import automation: {e}")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_automation_gpu()
'''
    
    with open("test_automation_gpu.py", "w") as f:
        f.write(test_script)
    
    print("✅ Created: test_automation_gpu.py")

def main():
    print("🚀 Quick CUDA Setup Check")
    print("=" * 40)
    
    # Check if CUDA toolkit exists
    cuda_exists = check_cuda_toolkit()
    
    if not cuda_exists:
        print("\n⚠️ CUDA Toolkit not found.")
        print("Your nvidia-smi shows CUDA 12.9 support, but toolkit isn't installed.")
        print("Please install CUDA Toolkit manually from:")
        print("https://developer.nvidia.com/cuda-downloads")
        print("Choose CUDA 12.x for Windows")
    
    # Install OpenCV regardless
    install_opencv_cuda_fast()
    
    # Test OpenCV CUDA
    if test_opencv_cuda():
        print("\n🎉 SUCCESS! GPU acceleration is working!")
        update_automation_script()
        print("\n🎯 Next steps:")
        print("1. Run: python test_automation_gpu.py")
        print("2. Run: python automation.py (choice 8)")
        print("3. Enjoy 3-5x faster processing!")
    else:
        print("\n⚠️ OpenCV CUDA not working yet.")
        print("Options:")
        print("1. Install CUDA Toolkit if missing")
        print("2. Try: conda install -c conda-forge opencv")
        print("3. Use current optimized CPU version (still fast!)")
        
        update_automation_script()
        print("\n✅ Your automation script is still optimized for CPU")

if __name__ == "__main__":
    main()
