import ffmpeg
import os

def trim_and_convert(input_path, end_time, temp_output):
    """Trim video to end_time (if given), convert to MPEG-TS format."""
    stream = ffmpeg.input(input_path, **({'t': end_time} if end_time else {}))
    stream = ffmpeg.output(stream, temp_output, format='mpegts', vcodec='copy', acodec='copy')
    ffmpeg.run(stream, overwrite_output=True)

def main():
    # Input 1
    path1 = input("Enter path to the first video: ").strip().strip('"')
    end1 = input("Enter end time for first video (seconds or HH:MM:SS, leave empty for full): ").strip()

    # Input 2
    path2 = input("Enter path to the second video: ").strip().strip('"')
    end2 = input("Enter end time for second video (seconds or HH:MM:SS, leave empty for full): ").strip()

    if not os.path.isfile(path1) or not os.path.isfile(path2):
        print("❌ One or both file paths are invalid.")
        return

    temp1 = "temp1.ts"
    temp2 = "temp2.ts"

    try:
        # Trim and convert
        trim_and_convert(path1, end1 if end1 else None, temp1)
        trim_and_convert(path2, end2 if end2 else None, temp2)

        # Output file name
        base1, ext1 = os.path.splitext(path1)
        output_path = f"{base1}_concat.mp4"

        # Concatenate both
        (
            ffmpeg
            .input(f"concat:{temp1}|{temp2}", format='mpegts')
            .output(output_path, vcodec='copy', acodec='copy')
            .overwrite_output()
            .run()
        )

        print(f"✅ Concatenated video saved to:\n{output_path}")

    except ffmpeg.Error as e:
        print("❌ FFmpeg error:\n", e.stderr.decode())
    finally:
        # Clean up
        for f in [temp1, temp2]:
            if os.path.exists(f):
                os.remove(f)

if __name__ == "__main__":
    main()
