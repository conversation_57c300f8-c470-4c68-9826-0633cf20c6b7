import subprocess
import sys
import os
import shutil

def check_cuda_installation():
    """Check if CUDA is properly installed"""
    print("Checking CUDA installation...")
    
    try:
        result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ CUDA Toolkit installed!")
            print(result.stdout)
            return True
        else:
            print("❌ CUDA Toolkit not found")
            return False
    except FileNotFoundError:
        print("❌ CUDA Toolkit not installed")
        print("Please install CUDA Toolkit first from:")
        print("https://developer.nvidia.com/cuda-downloads")
        return False

def install_opencv_with_conda():
    """Install OpenCV with CUDA using conda"""
    print("\nInstalling OpenCV with CUDA using conda...")
    
    # Check if conda is available
    try:
        subprocess.run(['conda', '--version'], capture_output=True, check=True)
        print("✅ Conda found")
    except (FileNotFoundError, subprocess.CalledProcessError):
        print("❌ Conda not found. Installing Miniconda...")
        print("Please download and install Miniconda from:")
        print("https://docs.conda.io/en/latest/miniconda.html")
        return False
    
    # Create environment and install packages
    commands = [
        ['conda', 'create', '-n', 'cuda_opencv', 'python=3.11', '-y'],
        ['conda', 'install', '-n', 'cuda_opencv', '-c', 'conda-forge', 'opencv', '-y'],
        ['conda', 'install', '-n', 'cuda_opencv', '-c', 'conda-forge', 'ffmpeg', 'tqdm', 'numpy', '-y']
    ]
    
    for cmd in commands:
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd)
        if result.returncode != 0:
            print(f"❌ Command failed: {' '.join(cmd)}")
            return False
    
    print("✅ Conda installation completed!")
    print("To use: conda activate cuda_opencv")
    return True

def test_opencv_cuda_conda():
    """Test OpenCV CUDA in conda environment"""
    print("\nTesting OpenCV CUDA in conda environment...")
    
    test_script = '''
import cv2
import numpy as np

print(f"OpenCV version: {cv2.__version__}")

# Check build info
build_info = cv2.getBuildInformation()
has_cuda = "CUDA" in build_info
print(f"CUDA support: {has_cuda}")

if has_cuda:
    try:
        device_count = cv2.cuda.getCudaEnabledDeviceCount()
        print(f"CUDA devices: {device_count}")
        
        if device_count > 0:
            # Test GPU operation
            test_img = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
            gpu_img = cv2.cuda_GpuMat()
            gpu_img.upload(test_img)
            gpu_gray = cv2.cuda.cvtColor(gpu_img, cv2.COLOR_BGR2GRAY)
            result = gpu_gray.download()
            print("SUCCESS: GPU operations working!")
        else:
            print("No CUDA devices detected")
    except Exception as e:
        print(f"CUDA operations failed: {e}")
else:
    print("OpenCV not compiled with CUDA")
'''
    
    # Test in conda environment
    cmd = ['conda', 'run', '-n', 'cuda_opencv', 'python', '-c', test_script]
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    print("Test Results:")
    print(result.stdout)
    if result.stderr:
        print("Errors:")
        print(result.stderr)
    
    return "SUCCESS: GPU operations working!" in result.stdout

def create_conda_automation_script():
    """Create script to run automation in conda environment"""
    script_content = '''@echo off
echo Activating CUDA OpenCV environment...
call conda activate cuda_opencv

echo Testing GPU support...
python -c "import cv2; print(f'CUDA devices: {cv2.cuda.getCudaEnabledDeviceCount()}')"

echo Running automation script...
python automation.py

pause
'''
    
    with open("run_automation_gpu.bat", "w") as f:
        f.write(script_content)
    
    print("✅ Created: run_automation_gpu.bat")
    print("Use this to run automation with GPU support")

def install_pip_opencv_cuda():
    """Try to install OpenCV with CUDA via pip (less reliable)"""
    print("\nTrying pip installation...")
    
    commands = [
        [sys.executable, "-m", "pip", "uninstall", "opencv-python", "opencv-contrib-python", "-y"],
        [sys.executable, "-m", "pip", "install", "opencv-contrib-python==********"],
    ]
    
    for cmd in commands:
        print(f"Running: {' '.join(cmd)}")
        subprocess.run(cmd)
    
    # Test
    try:
        import cv2
        device_count = cv2.cuda.getCudaEnabledDeviceCount()
        if device_count > 0:
            print("✅ Pip OpenCV with CUDA working!")
            return True
        else:
            print("❌ Pip OpenCV doesn't have CUDA support")
            return False
    except:
        print("❌ Pip OpenCV CUDA failed")
        return False

def main():
    print("🚀 OpenCV CUDA Installation")
    print("=" * 40)
    
    # Check CUDA first
    if not check_cuda_installation():
        print("\n❌ Please install CUDA Toolkit first!")
        print("1. Download from: https://developer.nvidia.com/cuda-downloads")
        print("2. Install with default settings")
        print("3. Restart computer")
        print("4. Run this script again")
        return
    
    print("\n✅ CUDA Toolkit is installed!")
    
    # Try conda approach first (most reliable)
    print("\n" + "=" * 40)
    print("Option 1: Conda installation (recommended)")
    
    if install_opencv_with_conda():
        if test_opencv_cuda_conda():
            print("\n🎉 SUCCESS! GPU acceleration working with conda!")
            create_conda_automation_script()
            print("\n🎯 To use GPU acceleration:")
            print("1. Run: run_automation_gpu.bat")
            print("2. Or manually: conda activate cuda_opencv && python automation.py")
            return
        else:
            print("❌ Conda OpenCV CUDA test failed")
    
    # Fallback to pip
    print("\n" + "=" * 40)
    print("Option 2: Pip installation (fallback)")
    
    if install_pip_opencv_cuda():
        print("\n🎉 SUCCESS! GPU acceleration working with pip!")
        print("Run: python automation.py (choice 8)")
    else:
        print("\n❌ Could not get OpenCV CUDA working")
        print("\nManual installation options:")
        print("1. Build OpenCV from source with CUDA")
        print("2. Use Docker with CUDA support")
        print("3. Use current CPU-optimized version (still fast!)")

if __name__ == "__main__":
    main()
