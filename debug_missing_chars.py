#!/usr/bin/env python3
"""
Debug script to find missing Tamil characters in our mapping
"""

import re
from tamil_to_english_transcript import TAMIL_TO_ROMAN, simple_tamil_transliterate

# Tamil unicode range
tamil_chars_re = re.compile(r'[\u0B80-\u0BFF]')

def analyze_missing_chars():
    """Analyze the missing Tamil characters"""
    
    # The characters found in the output file
    found_chars = {'த', 'ட', 'ப', 'வ', 'ீ', '்', 'ி', 'ா', 'க', 'ல', 'ன', 'ம'}
    
    print("🔍 Analyzing missing Tamil characters:")
    print("=" * 60)
    
    for char in found_chars:
        print(f"\nCharacter: '{char}' (U+{ord(char):04X})")
        
        # Check if it's in our mapping
        if char in TAMIL_TO_ROMAN:
            print(f"  ✅ In mapping: '{char}' → '{TAMIL_TO_ROMAN[char]}'")
        else:
            print(f"  ❌ MISSING from mapping!")
            
            # Get Unicode name
            import unicodedata
            try:
                name = unicodedata.name(char)
                print(f"  Unicode name: {name}")
            except:
                print(f"  Unicode name: Unknown")
        
        # Test transliteration
        test_result = simple_tamil_transliterate(char)
        print(f"  Transliteration result: '{test_result}'")
        
        # Check if Tamil characters remain
        has_tamil = bool(tamil_chars_re.search(test_result))
        print(f"  Tamil chars remaining: {has_tamil}")
    
    print("\n" + "=" * 60)
    print("Analysis completed!")

if __name__ == "__main__":
    analyze_missing_chars()
