import os
import cv2
import numpy as np
import glob
from tqdm import tqdm

def is_cuda_available():
    try:
        device_count = cv2.cuda.getCudaEnabledDeviceCount()
        if device_count > 0:
            print(f"🔍 CUDA devices detected: {device_count}")
            return True
        else:
            print("❌ No CUDA devices found")
            return False
    except Exception as e:
        print(f"❌ CUDA not available: {e}")
        if "No CUDA support" in str(e):
            print("💡 Your OpenCV installation doesn't have CUDA support.")
            print("   Using optimized CPU processing instead...")
        return False

def add_padding_with_transparency(frame, pad=20, use_cuda=False):
    h, w = frame.shape[:2]
    
    if use_cuda:
        try:
            if frame.shape[2] == 3:
                gpu_frame = cv2.cuda_GpuMat()
                gpu_frame.upload(frame)
                gpu_frame_bgra = cv2.cuda.cvtColor(gpu_frame, cv2.COLOR_BGR2BGRA)
                frame = gpu_frame_bgra.download()
        except Exception:
            if frame.shape[2] == 3:
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2BGRA)
    else:
        if frame.shape[2] == 3:
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2BGRA)
    
    new_frame = np.zeros((h + pad * 2, w + pad * 2, 4), dtype=np.uint8)
    new_frame[pad:pad + h, pad:pad + w] = frame
    return new_frame

def overlay_image_alpha(background, overlay, use_cuda=False):
    if overlay.shape[:2] != background.shape[:2]:
        if use_cuda:
            try:
                gpu_overlay = cv2.cuda_GpuMat()
                gpu_overlay.upload(overlay)
                gpu_resized = cv2.cuda.resize(gpu_overlay, (background.shape[1], background.shape[0]))
                overlay = gpu_resized.download()
            except Exception:
                overlay = cv2.resize(overlay, (background.shape[1], background.shape[0]))
        else:
            overlay = cv2.resize(overlay, (background.shape[1], background.shape[0]))
    
    if overlay.shape[2] != 4:
        if use_cuda:
            try:
                gpu_overlay = cv2.cuda_GpuMat()
                gpu_overlay.upload(overlay)
                gpu_overlay_bgra = cv2.cuda.cvtColor(gpu_overlay, cv2.COLOR_BGR2BGRA)
                overlay = gpu_overlay_bgra.download()
            except Exception:
                overlay = cv2.cvtColor(overlay, cv2.COLOR_BGR2BGRA)
        else:
            overlay = cv2.cvtColor(overlay, cv2.COLOR_BGR2BGRA)

    alpha_overlay = overlay[:, :, 3:4].astype(np.float32) / 255.0
    alpha_background = 1.0 - alpha_overlay

    overlay_rgb = overlay[:, :, :3].astype(np.float32)
    background_rgb = background[:, :, :3].astype(np.float32)
    
    result = np.zeros_like(background, dtype=np.uint8)
    result[:, :, :3] = (alpha_overlay * overlay_rgb + alpha_background * background_rgb).astype(np.uint8)
    result[:, :, 3] = background[:, :, 3]
    
    return result

def process_video(video_path, overlay_img, output_path, use_cuda=False):
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"⚠️ Couldn't open video: {video_path}")
        return

    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    fps = cap.get(cv2.CAP_PROP_FPS)
    w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    out_w, out_h = w + 40, h + 40
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    out = cv2.VideoWriter(output_path, fourcc, fps, (out_w, out_h), True)
    if not out.isOpened():
        print(f"⚠️ Couldn't write video: {output_path}")
        return

    print(f"📊 Processing {total_frames} frames at {fps:.2f} FPS")
    print(f"📐 Input: {w}x{h} → Output: {out_w}x{out_h}")
    
    overlay_resized = cv2.resize(overlay_img, (out_w, out_h))
    if overlay_resized.shape[2] != 4:
        overlay_resized = cv2.cvtColor(overlay_resized, cv2.COLOR_BGR2BGRA)

    pbar = tqdm(total=total_frames, desc=os.path.basename(video_path), unit="frames")
    frame_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break

        if use_cuda:
            try:
                gpu_frame = cv2.cuda_GpuMat()
                gpu_frame.upload(frame)
                gpu_frame_bgra = cv2.cuda.cvtColor(gpu_frame, cv2.COLOR_BGR2BGRA)
                frame = gpu_frame_bgra.download()
            except Exception:
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2BGRA)
        else:
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2BGRA)

        padded_frame = add_padding_with_transparency(frame, use_cuda=use_cuda)
        result = overlay_image_alpha(padded_frame, overlay_resized, use_cuda=use_cuda)

        if use_cuda:
            try:
                gpu_result = cv2.cuda_GpuMat()
                gpu_result.upload(result)
                gpu_result_bgr = cv2.cuda.cvtColor(gpu_result, cv2.COLOR_BGRA2BGR)
                result_bgr = gpu_result_bgr.download()
            except Exception:
                result_bgr = cv2.cvtColor(result, cv2.COLOR_BGRA2BGR)
        else:
            result_bgr = cv2.cvtColor(result, cv2.COLOR_BGRA2BGR)

        out.write(result_bgr)
        frame_count += 1
        pbar.update(1)
        
        if frame_count % 100 == 0:
            pbar.set_postfix({"processed": f"{frame_count}/{total_frames}"})
    
    pbar.close()
    cap.release()
    out.release()
    
    print(f"✅ Processed {frame_count} frames successfully")

def main():
    test_video_path = r"D:\testing\marumagal\original_video"
    
    if not os.path.exists(test_video_path):
        print(f"❌ Test video path not found: {test_video_path}")
        return
        
    input_dir = os.path.abspath(test_video_path)
    output_dir = os.path.join(input_dir, "overlayed_logo")
    
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Create a test overlay image
    test_overlay = np.zeros((100, 200, 4), dtype=np.uint8)
    test_overlay[:, :, 0] = 255  # Blue channel
    test_overlay[:, :, 3] = 128  # Alpha channel (semi-transparent)
    cv2.putText(test_overlay, "TEST LOGO", (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255, 255), 2)

    use_cuda = is_cuda_available()
    print(f"⚙️  GPU acceleration: {'Enabled' if use_cuda else 'Disabled'}")

    videos = glob.glob(os.path.join(input_dir, "*.mp4")) + glob.glob(os.path.join(input_dir, "*.mov"))
    
    if not videos:
        print(f"❌ No video files found in: {input_dir}")
        return
        
    print(f"📹 Found {len(videos)} video(s) to process")
    
    for video_path in videos:
        filename = os.path.basename(video_path)
        output_path = os.path.join(output_dir, filename)
        print(f"🎬 Processing: {filename}")
        process_video(video_path, test_overlay, output_path, use_cuda)
        print(f"✅ Completed: {filename}")
        
    print(f"🎉 All videos processed! Output saved to: {output_dir}")

if __name__ == "__main__":
    main()
