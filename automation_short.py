import os
import cv2
import numpy as np
import time
import glob
from tqdm import tqdm

# GPU imports
try:
    import torch
    import torch.nn.functional as F
    GPU_AVAILABLE = torch.cuda.is_available()
    DEVICE = 'cuda' if GPU_AVAILABLE else 'cpu'
except ImportError:
    GPU_AVAILABLE = False
    DEVICE = 'cpu'

def gpu_process_frame(frame, overlay, pad=20):
    """GPU-accelerated frame processing"""
    if not GPU_AVAILABLE:
        return cpu_process_frame(frame, overlay, pad)
    
    try:
        h, w = frame.shape[:2]
        
        # Convert to tensor and add alpha
        frame_tensor = torch.from_numpy(frame).float().to(DEVICE)
        alpha = torch.full((h, w, 1), 255.0, device=DEVICE)
        frame_bgra = torch.cat([frame_tensor, alpha], dim=2)
        
        # Add padding
        padded = F.pad(frame_bgra.permute(2, 0, 1), (pad, pad, pad, pad)).permute(1, 2, 0)
        
        # Resize overlay and blend
        overlay_tensor = torch.from_numpy(overlay).float().to(DEVICE)
        if overlay_tensor.shape[:2] != padded.shape[:2]:
            overlay_tensor = F.interpolate(
                overlay_tensor.permute(2, 0, 1).unsqueeze(0),
                size=padded.shape[:2], mode='bilinear'
            ).squeeze(0).permute(1, 2, 0)
        
        # Alpha blending
        alpha_overlay = overlay_tensor[:, :, 3:4] / 255.0
        result = padded.clone()
        result[:, :, :3] = (1 - alpha_overlay) * padded[:, :, :3] + alpha_overlay * overlay_tensor[:, :, :3]
        
        return result[:, :, :3].cpu().numpy().astype(np.uint8)
    except Exception as e:
        print(f"GPU failed, using CPU: {e}")
        return cpu_process_frame(frame, overlay, pad)

def cpu_process_frame(frame, overlay, pad=20):
    """CPU fallback processing"""
    h, w = frame.shape[:2]
    padded = np.zeros((h + pad * 2, w + pad * 2, 3), dtype=np.uint8)
    padded[pad:pad + h, pad:pad + w] = frame
    
    # Simple overlay
    overlay_resized = cv2.resize(overlay, (padded.shape[1], padded.shape[0]))
    if overlay_resized.shape[2] == 4:
        alpha = overlay_resized[:, :, 3:4] / 255.0
        for c in range(3):
            padded[:, :, c] = padded[:, :, c] * (1 - alpha[:, :, 0]) + overlay_resized[:, :, c] * alpha[:, :, 0]
    
    return padded

def process_video_overlay(video_path, overlay_img, output_path):
    """Optimized video overlay processing"""
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"❌ Cannot open: {video_path}")
        return
    
    fps = cap.get(cv2.CAP_PROP_FPS)
    w, h = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)), int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (w + 40, h + 40))
    
    print(f"📊 Processing {total_frames} frames with {'GPU' if GPU_AVAILABLE else 'CPU'}")
    
    pbar = tqdm(total=total_frames, desc=os.path.basename(video_path), unit="fps")
    start_time = time.time()
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        processed = gpu_process_frame(frame, overlay_img) if GPU_AVAILABLE else cpu_process_frame(frame, overlay_img)
        out.write(processed)
        pbar.update(1)
    
    elapsed = time.time() - start_time
    fps_actual = total_frames / elapsed if elapsed > 0 else 0
    
    pbar.close()
    cap.release()
    out.release()
    
    print(f"✅ Completed in {elapsed:.1f}s ({fps_actual:.1f} FPS)")

def main():
    print("🚀 Video Processing Tool (Optimized)")
    if GPU_AVAILABLE:
        print(f"🔥 GPU: {torch.cuda.get_device_name(0)}")
    else:
        print("💻 Using CPU processing")
    
    # Direct path to test video
    test_path = r"D:\testing\marumagal\original_video"
    
    if not os.path.exists(test_path):
        print(f"❌ Path not found: {test_path}")
        return
    
    output_dir = os.path.join(test_path, "overlayed_logo")
    os.makedirs(output_dir, exist_ok=True)
    
    # Create test overlay
    overlay = np.zeros((100, 200, 4), dtype=np.uint8)
    overlay[:, :, 0] = 255  # Blue
    overlay[:, :, 3] = 128  # Alpha
    cv2.putText(overlay, "GPU LOGO", (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255, 255), 2)
    
    videos = glob.glob(os.path.join(test_path, "*.mp4")) + glob.glob(os.path.join(test_path, "*.mov"))
    
    if not videos:
        print(f"❌ No videos found in: {test_path}")
        return
    
    print(f"📹 Found {len(videos)} video(s)")
    
    for video_path in videos:
        filename = os.path.basename(video_path)
        output_path = os.path.join(output_dir, filename)
        print(f"\n🎬 Processing: {filename}")
        process_video_overlay(video_path, overlay, output_path)
    
    print(f"\n🎉 All done! Output: {output_dir}")

if __name__ == "__main__":
    main()
